from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm, mm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, PageBreak
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT, TA_JUSTIFY
from django.conf import settings
from django.core.files.base import ContentFile
from .models import Entreprise
from django.utils import timezone
import os
from datetime import datetime


def generer_pdf(document):
    """Génère un PDF pour un document donné avec design amélioré"""

    # Créer le répertoire pdfs s'il n'existe pas
    pdf_dir = os.path.join(settings.MEDIA_ROOT, 'pdfs')
    os.makedirs(pdf_dir, exist_ok=True)

    # Nom du fichier
    filename = f"{document.type_document}_{document.numero}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
    filepath = os.path.join(pdf_dir, filename)

    # Créer le document PDF avec marges personnalisées
    doc = SimpleDocTemplate(
        filepath,
        pagesize=A4,
        topMargin=1.5*cm,
        bottomMargin=2*cm,
        leftMargin=2*cm,
        rightMargin=2*cm
    )

    # Styles personnalisés
    styles = getSampleStyleSheet()

    # Style pour le titre principal
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=20,
        textColor=colors.HexColor('#2c3e50'),
        alignment=TA_CENTER,
        fontName='Helvetica-Bold'
    )

    # Style pour les en-têtes de section
    section_style = ParagraphStyle(
        'SectionHeader',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=10,
        spaceBefore=15,
        textColor=colors.HexColor('#34495e'),
        fontName='Helvetica-Bold',
        borderWidth=1,
        borderColor=colors.HexColor('#3498db'),
        borderPadding=5,
        backColor=colors.HexColor('#ecf0f1')
    )

    # Style pour le texte normal
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=5,
        textColor=colors.HexColor('#2c3e50'),
        fontName='Helvetica'
    )

    # Style pour les informations importantes
    important_style = ParagraphStyle(
        'Important',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=5,
        textColor=colors.HexColor('#e74c3c'),
        fontName='Helvetica-Bold'
    )

    # Contenu du PDF
    story = []

    # === EN-TÊTE AVEC LOGO ET INFORMATIONS ENTREPRISE ===
    try:
        entreprise = Entreprise.objects.first()
        if entreprise:
            # Créer un tableau pour l'en-tête avec logo et informations
            header_data = []

            # Vérifier si un logo existe
            logo_cell = ""
            if entreprise.logo and os.path.exists(entreprise.logo.path):
                try:
                    # Ajouter le logo
                    logo = Image(entreprise.logo.path, width=3*cm, height=2*cm)
                    logo_cell = logo
                except:
                    logo_cell = ""

            # Informations de l'entreprise
            company_info = f"""
            <font size="16" color="#2c3e50"><b>{entreprise.nom}</b></font><br/>
            <font size="10" color="#34495e">{entreprise.adresse}</font><br/>
            <font size="10" color="#34495e">{entreprise.code_postal} {entreprise.ville}</font><br/>
            """

            if entreprise.telephone:
                company_info += f'<font size="10" color="#34495e">Tél: {entreprise.telephone}</font><br/>'
            if entreprise.email:
                company_info += f'<font size="10" color="#34495e">Email: {entreprise.email}</font><br/>'
            if entreprise.siret:
                company_info += f'<font size="10" color="#7f8c8d">SIRET: {entreprise.siret}</font><br/>'
            if entreprise.tva_numero:
                company_info += f'<font size="10" color="#7f8c8d">TVA: {entreprise.tva_numero}</font>'

            # Créer le tableau d'en-tête
            if logo_cell:
                header_table = Table([[logo_cell, Paragraph(company_info, normal_style)]],
                                   colWidths=[4*cm, 12*cm])
            else:
                header_table = Table([[Paragraph(company_info, normal_style)]],
                                   colWidths=[16*cm])

            header_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                ('TOPPADDING', (0, 0), (-1, -1), 0),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ]))

            story.append(header_table)
            story.append(Spacer(1, 15))

            # Ligne de séparation
            line_table = Table([['']], colWidths=[17*cm])
            line_table.setStyle(TableStyle([
                ('LINEBELOW', (0, 0), (-1, -1), 2, colors.HexColor('#3498db')),
            ]))
            story.append(line_table)
            story.append(Spacer(1, 20))

    except Exception as e:
        print(f"Erreur lors de la création de l'en-tête: {e}")

    # === TITRE DU DOCUMENT ===
    titre = document.get_type_document_display().upper()
    story.append(Paragraph(f"{titre} N° {document.numero}", title_style))
    story.append(Spacer(1, 15))

    # === INFORMATIONS CLIENT ET DOCUMENT ===
    client = document.client

    # Informations client
    client_info = f"""
    <font size="12" color="#2c3e50"><b>معلومات العميل / Informations Client</b></font><br/>
    """
    if client.entreprise:
        client_info += f'<font size="11" color="#e74c3c"><b>{client.entreprise}</b></font><br/>'

    client_info += f"""
    <font size="10" color="#34495e">{client.nom} {client.prenom}</font><br/>
    <font size="10" color="#34495e">{client.adresse}</font><br/>
    <font size="10" color="#34495e">{client.code_postal} {client.ville}</font><br/>
    """

    if client.email:
        client_info += f'<font size="10" color="#34495e">Email: {client.email}</font><br/>'
    if client.telephone:
        client_info += f'<font size="10" color="#34495e">Tél: {client.telephone}</font>'

    # Informations du document
    doc_info = f"""
    <font size="12" color="#2c3e50"><b>معلومات الوثيقة / Informations Document</b></font><br/>
    <font size="10" color="#34495e"><b>التاريخ / Date:</b> {document.date_document.strftime('%d/%m/%Y')}</font><br/>
    <font size="10" color="#34495e"><b>الحالة / Statut:</b> {document.get_statut_display()}</font><br/>
    """

    if document.date_echeance:
        doc_info += f'<font size="10" color="#e74c3c"><b>تاريخ الاستحقاق / Échéance:</b> {document.date_echeance.strftime("%d/%m/%Y")}</font><br/>'

    # Créer tableau pour client et document
    info_table = Table([
        [Paragraph(client_info, normal_style), Paragraph(doc_info, normal_style)]
    ], colWidths=[8.5*cm, 8.5*cm])

    info_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('BACKGROUND', (0, 0), (0, 0), colors.HexColor('#f8f9fa')),
        ('BACKGROUND', (1, 0), (1, 0), colors.HexColor('#f8f9fa')),
        ('BOX', (0, 0), (-1, -1), 1, colors.HexColor('#bdc3c7')),
        ('INNERGRID', (0, 0), (-1, -1), 1, colors.HexColor('#bdc3c7')),
        ('LEFTPADDING', (0, 0), (-1, -1), 10),
        ('RIGHTPADDING', (0, 0), (-1, -1), 10),
        ('TOPPADDING', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
    ]))

    story.append(info_table)
    story.append(Spacer(1, 25))

    # === TABLEAU DES PRODUITS/SERVICES ===
    # En-tête du tableau avec style bilingue
    data_lignes = [[
        'الوصف / Description',
        'الكمية\nQté',
        'السعر الوحدوي\nPrix unit.',
        'الضريبة\nTVA',
        'المجموع\nTotal HT'
    ]]

    # Ajouter les lignes de produits
    for ligne in document.lignes.all():
        description = ligne.description or ligne.produit.nom
        data_lignes.append([
            description,
            f"{ligne.quantite}",
            f"{ligne.prix_unitaire:.2f} €",
            f"{ligne.tva_taux:.1f}%",
            f"{ligne.total_ht:.2f} €"
        ])

    # Créer le tableau avec style amélioré
    table_lignes = Table(data_lignes, colWidths=[7*cm, 2.5*cm, 2.5*cm, 2.5*cm, 2.5*cm])
    table_lignes.setStyle(TableStyle([
        # Style de l'en-tête
        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('ALIGN', (0, 1), (0, -1), 'LEFT'),  # Description alignée à gauche
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('FONTSIZE', (0, 1), (-1, -1), 9),

        # Style du corps
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.HexColor('#2c3e50')),
        ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#bdc3c7')),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('LEFTPADDING', (0, 0), (-1, -1), 8),
        ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),

        # Lignes alternées
        ('BACKGROUND', (0, 2), (-1, -1), colors.HexColor('#f8f9fa')),
        ('BACKGROUND', (0, 1), (-1, 1), colors.white),
    ]))

    # Ajouter des couleurs alternées pour les lignes
    for i in range(1, len(data_lignes)):
        if i % 2 == 0:
            table_lignes.setStyle(TableStyle([
                ('BACKGROUND', (0, i), (-1, i), colors.HexColor('#f8f9fa'))
            ]))

    story.append(table_lignes)
    story.append(Spacer(1, 25))

    # === SECTION DES TOTAUX ===
    # Créer un tableau pour les totaux avec style amélioré
    data_totaux = [
        ['المجموع الفرعي / Sous-total HT:', f"{document.sous_total:.2f} €"],
        ['مجموع الضريبة / Total TVA:', f"{document.total_tva:.2f} €"],
        ['', ''],  # Ligne vide pour séparation
        ['المجموع الإجمالي / TOTAL TTC:', f"{document.total_ttc:.2f} €"],
    ]

    table_totaux = Table(data_totaux, colWidths=[10*cm, 4*cm])
    table_totaux.setStyle(TableStyle([
        # Style général
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('FONTSIZE', (0, 0), (-1, -2), 11),
        ('FONTSIZE', (0, -1), (-1, -1), 14),
        ('TEXTCOLOR', (0, 0), (-1, -2), colors.HexColor('#34495e')),
        ('TEXTCOLOR', (0, -1), (-1, -1), colors.HexColor('#e74c3c')),

        # Style pour les deux premières lignes
        ('FONTNAME', (0, 0), (-1, 1), 'Helvetica'),
        ('BOTTOMPADDING', (0, 0), (-1, 1), 8),
        ('TOPPADDING', (0, 0), (-1, 1), 8),

        # Ligne de séparation
        ('LINEABOVE', (0, 2), (-1, 2), 1, colors.HexColor('#bdc3c7')),
        ('FONTSIZE', (0, 2), (-1, 2), 6),

        # Style pour le total final
        ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
        ('BACKGROUND', (0, -1), (-1, -1), colors.HexColor('#f8f9fa')),
        ('BOX', (0, -1), (-1, -1), 2, colors.HexColor('#e74c3c')),
        ('LEFTPADDING', (0, -1), (-1, -1), 10),
        ('RIGHTPADDING', (0, -1), (-1, -1), 10),
        ('TOPPADDING', (0, -1), (-1, -1), 10),
        ('BOTTOMPADDING', (0, -1), (-1, -1), 10),
    ]))

    # Aligner le tableau des totaux à droite
    totaux_container = Table([[table_totaux]], colWidths=[17*cm])
    totaux_container.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('LEFTPADDING', (0, 0), (-1, -1), 0),
        ('RIGHTPADDING', (0, 0), (-1, -1), 0),
        ('TOPPADDING', (0, 0), (-1, -1), 0),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
    ]))

    story.append(totaux_container)
    story.append(Spacer(1, 25))

    # === NOTES ===
    if document.notes:
        notes_title = Paragraph(
            '<font size="12" color="#2c3e50"><b>ملاحظات / Notes:</b></font>',
            normal_style
        )
        story.append(notes_title)
        story.append(Spacer(1, 10))

        notes_content = Paragraph(
            f'<font size="10" color="#34495e">{document.notes}</font>',
            normal_style
        )
        story.append(notes_content)
        story.append(Spacer(1, 20))

    # === PIED DE PAGE ===
    footer_text = f"""
    <font size="8" color="#7f8c8d">
    تم إنشاء هذه الوثيقة بتاريخ {datetime.now().strftime('%d/%m/%Y في %H:%M')}<br/>
    Document généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}<br/>
    شكراً لثقتكم بنا / Merci pour votre confiance
    </font>
    """

    footer = Paragraph(footer_text, ParagraphStyle(
        'Footer',
        parent=normal_style,
        alignment=TA_CENTER,
        fontSize=8,
        textColor=colors.HexColor('#7f8c8d')
    ))

    story.append(Spacer(1, 30))
    story.append(footer)

    # Générer le PDF
    doc.build(story)

    # Sauvegarder le fichier dans le modèle
    with open(filepath, 'rb') as f:
        document.fichier_pdf.save(
            filename,
            ContentFile(f.read()),
            save=True
        )

    # Supprimer le fichier temporaire
    if os.path.exists(filepath):
        os.remove(filepath)

    return document.fichier_pdf


def generer_numero_document(type_doc):
    """Génère un numéro unique pour un document"""
    from .models import Document

    # Préfixes selon le type
    prefixes = {
        'facture': 'FAC',
        'devis': 'DEV',
        'bon_livraison': 'BL'
    }

    prefix = prefixes.get(type_doc, 'DOC')
    year = datetime.now().year

    # Trouver le dernier numéro pour ce type et cette année
    last_doc = Document.objects.filter(
        type_document=type_doc,
        numero__startswith=f"{prefix}{year}"
    ).order_by('-numero').first()

    if last_doc:
        try:
            last_num = int(last_doc.numero.split('-')[-1])
            new_num = last_num + 1
        except (ValueError, IndexError):
            new_num = 1
    else:
        new_num = 1

    return f"{prefix}{year}-{new_num:04d}"
