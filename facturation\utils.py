from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.pdfgen import canvas
from django.conf import settings
from django.core.files.base import ContentFile
from .models import Entreprise
from django.utils import timezone
import os
from datetime import datetime


def generer_pdf(document):
    """Génère un PDF pour un document donné"""

    # Créer le répertoire pdfs s'il n'existe pas
    pdf_dir = os.path.join(settings.MEDIA_ROOT, 'pdfs')
    os.makedirs(pdf_dir, exist_ok=True)

    # Nom du fichier
    filename = f"{document.type_document}_{document.numero}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
    filepath = os.path.join(pdf_dir, filename)

    # Créer le document PDF
    doc = SimpleDocTemplate(filepath, pagesize=A4, topMargin=2*cm, bottomMargin=2*cm)

    # Styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        textColor=colors.darkblue
    )

    # Contenu du PDF
    story = []

    # En-tête avec informations de l'entreprise
    try:
        entreprise = Entreprise.objects.first()
        if entreprise:
            story.append(Paragraph(f"<b>{entreprise.nom}</b>", styles['Normal']))
            story.append(Paragraph(entreprise.adresse, styles['Normal']))
            story.append(Paragraph(f"{entreprise.code_postal} {entreprise.ville}", styles['Normal']))
            if entreprise.telephone:
                story.append(Paragraph(f"Tél: {entreprise.telephone}", styles['Normal']))
            if entreprise.email:
                story.append(Paragraph(f"Email: {entreprise.email}", styles['Normal']))
            if entreprise.siret:
                story.append(Paragraph(f"SIRET: {entreprise.siret}", styles['Normal']))
            story.append(Spacer(1, 20))
    except:
        pass

    # Titre du document
    titre = document.get_type_document_display().upper()
    story.append(Paragraph(f"<b>{titre} N° {document.numero}</b>", title_style))

    # Informations client
    story.append(Paragraph("<b>Client:</b>", styles['Heading2']))
    client = document.client
    if client.entreprise:
        story.append(Paragraph(f"<b>{client.entreprise}</b>", styles['Normal']))
    story.append(Paragraph(f"{client.nom} {client.prenom}", styles['Normal']))
    story.append(Paragraph(client.adresse, styles['Normal']))
    story.append(Paragraph(f"{client.code_postal} {client.ville}", styles['Normal']))
    if client.email:
        story.append(Paragraph(f"Email: {client.email}", styles['Normal']))
    story.append(Spacer(1, 20))

    # Informations du document
    data_info = [
        ['Date du document:', document.date_document.strftime('%d/%m/%Y')],
        ['Statut:', document.get_statut_display()],
    ]

    if document.date_echeance:
        data_info.append(['Date d\'échéance:', document.date_echeance.strftime('%d/%m/%Y')])

    table_info = Table(data_info, colWidths=[4*cm, 6*cm])
    table_info.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))
    story.append(table_info)
    story.append(Spacer(1, 20))

    # Tableau des lignes
    data_lignes = [['Description', 'Qté', 'Prix unit.', 'TVA', 'Total HT']]

    for ligne in document.lignes.all():
        description = ligne.description or ligne.produit.nom
        data_lignes.append([
            description,
            f"{ligne.quantite}",
            f"{ligne.prix_unitaire:.2f} €",
            f"{ligne.tva_taux:.1f}%",
            f"{ligne.total_ht:.2f} €"
        ])

    table_lignes = Table(data_lignes, colWidths=[8*cm, 2*cm, 2.5*cm, 2*cm, 2.5*cm])
    table_lignes.setStyle(TableStyle([
        # En-tête
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('ALIGN', (0, 1), (0, -1), 'LEFT'),  # Description alignée à gauche
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 9),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),

        # Corps du tableau
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))
    story.append(table_lignes)
    story.append(Spacer(1, 20))

    # Totaux
    data_totaux = [
        ['Sous-total HT:', f"{document.sous_total:.2f} €"],
        ['Total TVA:', f"{document.total_tva:.2f} €"],
        ['TOTAL TTC:', f"{document.total_ttc:.2f} €"],
    ]

    table_totaux = Table(data_totaux, colWidths=[4*cm, 3*cm])
    table_totaux.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('LINEABOVE', (0, -1), (-1, -1), 2, colors.black),
    ]))
    story.append(table_totaux)

    # Notes
    if document.notes:
        story.append(Spacer(1, 20))
        story.append(Paragraph("<b>Notes:</b>", styles['Heading3']))
        story.append(Paragraph(document.notes, styles['Normal']))

    # Générer le PDF
    doc.build(story)

    # Sauvegarder le fichier dans le modèle
    with open(filepath, 'rb') as f:
        document.fichier_pdf.save(
            filename,
            ContentFile(f.read()),
            save=True
        )

    # Supprimer le fichier temporaire
    if os.path.exists(filepath):
        os.remove(filepath)

    return document.fichier_pdf


def generer_numero_document(type_doc):
    """Génère un numéro unique pour un document"""
    from .models import Document

    # Préfixes selon le type
    prefixes = {
        'facture': 'FAC',
        'devis': 'DEV',
        'bon_livraison': 'BL'
    }

    prefix = prefixes.get(type_doc, 'DOC')
    year = datetime.now().year

    # Trouver le dernier numéro pour ce type et cette année
    last_doc = Document.objects.filter(
        type_document=type_doc,
        numero__startswith=f"{prefix}{year}"
    ).order_by('-numero').first()

    if last_doc:
        try:
            last_num = int(last_doc.numero.split('-')[-1])
            new_num = last_num + 1
        except (ValueError, IndexError):
            new_num = 1
    else:
        new_num = 1

    return f"{prefix}{year}-{new_num:04d}"
