{% extends 'facturation/base.html' %}

{% block title %}{{ document.get_type_document_display }} {{ document.numero }} - Facturation Pro{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ document.get_type_document_display }} {{ document.numero }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'facturation:modifier_document' document.pk %}" class="btn btn-outline-primary">
                <i class="bi bi-pencil"></i> Modifier
            </a>
            <a href="{% url 'facturation:telecharger_pdf' document.pk %}" class="btn btn-success">
                <i class="bi bi-download"></i> Télécharger PDF
            </a>
        </div>
        <a href="{% url 'facturation:liste_documents' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Retour à la liste
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Informations du document -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informations générales</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Numéro:</strong></td>
                                <td>{{ document.numero }}</td>
                            </tr>
                            <tr>
                                <td><strong>Type:</strong></td>
                                <td>
                                    {% if document.type_document == 'facture' %}
                                        <span class="badge bg-primary">{{ document.get_type_document_display }}</span>
                                    {% elif document.type_document == 'devis' %}
                                        <span class="badge bg-info">{{ document.get_type_document_display }}</span>
                                    {% else %}
                                        <span class="badge bg-success">{{ document.get_type_document_display }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Date du document:</strong></td>
                                <td>{{ document.date_document|date:"d/m/Y" }}</td>
                            </tr>
                            {% if document.date_echeance %}
                            <tr>
                                <td><strong>Date d'échéance:</strong></td>
                                <td>{{ document.date_echeance|date:"d/m/Y" }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Statut:</strong></td>
                                <td>
                                    {% if document.statut == 'brouillon' %}
                                        <span class="badge bg-secondary">{{ document.get_statut_display }}</span>
                                    {% elif document.statut == 'envoye' %}
                                        <span class="badge bg-warning">{{ document.get_statut_display }}</span>
                                    {% elif document.statut == 'paye' %}
                                        <span class="badge bg-success">{{ document.get_statut_display }}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{{ document.get_statut_display }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Date de création:</strong></td>
                                <td>{{ document.date_creation|date:"d/m/Y H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if document.notes %}
                <div class="mt-3">
                    <strong>Notes:</strong>
                    <p class="mt-2">{{ document.notes|linebreaks }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Lignes du document -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Détail des lignes</h5>
            </div>
            <div class="card-body">
                {% if lignes %}
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Description</th>
                                    <th class="text-end">Quantité</th>
                                    <th class="text-end">Prix unitaire</th>
                                    <th class="text-end">TVA</th>
                                    <th class="text-end">Total HT</th>
                                    <th class="text-end">Total TTC</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ligne in lignes %}
                                <tr>
                                    <td>
                                        <strong>{{ ligne.produit.nom }}</strong>
                                        {% if ligne.description %}
                                            <br><small class="text-muted">{{ ligne.description }}</small>
                                        {% endif %}
                                    </td>
                                    <td class="text-end">{{ ligne.quantite }}</td>
                                    <td class="text-end">{{ ligne.prix_unitaire|floatformat:2 }}€</td>
                                    <td class="text-end">{{ ligne.tva_taux|floatformat:1 }}%</td>
                                    <td class="text-end">{{ ligne.total_ht|floatformat:2 }}€</td>
                                    <td class="text-end"><strong>{{ ligne.total_ttc|floatformat:2 }}€</strong></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">Aucune ligne dans ce document.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Informations client -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Client</h5>
            </div>
            <div class="card-body">
                {% if document.client.entreprise %}
                    <h6>{{ document.client.entreprise }}</h6>
                {% endif %}
                <p class="mb-1">{{ document.client.nom }} {{ document.client.prenom }}</p>
                <p class="mb-1">{{ document.client.adresse|linebreaks }}</p>
                <p class="mb-1">{{ document.client.code_postal }} {{ document.client.ville }}</p>
                {% if document.client.email %}
                    <p class="mb-1">
                        <i class="bi bi-envelope"></i> 
                        <a href="mailto:{{ document.client.email }}">{{ document.client.email }}</a>
                    </p>
                {% endif %}
                {% if document.client.telephone %}
                    <p class="mb-0">
                        <i class="bi bi-telephone"></i> {{ document.client.telephone }}
                    </p>
                {% endif %}
            </div>
        </div>

        <!-- Totaux -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Totaux</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td>Sous-total HT:</td>
                        <td class="text-end">{{ document.sous_total|floatformat:2 }}€</td>
                    </tr>
                    <tr>
                        <td>Total TVA:</td>
                        <td class="text-end">{{ document.total_tva|floatformat:2 }}€</td>
                    </tr>
                    <tr class="border-top">
                        <td><strong>TOTAL TTC:</strong></td>
                        <td class="text-end"><strong class="h5">{{ document.total_ttc|floatformat:2 }}€</strong></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Actions rapides</h5>
            </div>
            <div class="card-body">
                {% if document.type_document == 'devis' %}
                    <a href="#" class="btn btn-primary w-100 mb-2">
                        <i class="bi bi-arrow-right"></i> Convertir en facture
                    </a>
                {% endif %}
                
                <a href="{% url 'facturation:telecharger_pdf' document.pk %}" class="btn btn-success w-100 mb-2">
                    <i class="bi bi-download"></i> Télécharger PDF
                </a>
                
                <a href="{% url 'facturation:modifier_document' document.pk %}" class="btn btn-outline-primary w-100">
                    <i class="bi bi-pencil"></i> Modifier
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
