/* ===== THÈME EXEMPLE - ENTREPRISE CRÉATIVE ===== */

:root {
    --primary-color: #ff6b6b;
    --secondary-color: #4ecdc4;
    --success-color: #51cf66;
    --danger-color: #ff8787;
    --warning-color: #ffd43b;
    --info-color: #74c0fc;
    --light-color: #f8f9fa;
    --dark-color: #495057;
    
    --gradient-primary: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%);
    --gradient-secondary: linear-gradient(135deg, #4ecdc4 0%, #81e6d9 100%);
    --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* ===== NAVIGATION CRÉATIVE ===== */
.navbar-dark {
    background: var(--gradient-primary) !important;
    box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3);
    border-bottom: 3px solid #ffa8a8;
}

.navbar-brand {
    font-family: 'Comic Sans MS', cursive, sans-serif;
    font-size: 1.8rem;
    font-weight: 900;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    color: white !important;
}

.navbar-brand i {
    color: #ffd43b;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.nav-link {
    font-weight: 600;
    border-radius: 20px;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* ===== CARTES CRÉATIVES ===== */
.card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    background: white;
    overflow: hidden;
    position: relative;
    transition: all 0.4s ease;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.card:hover {
    transform: translateY(-10px) rotate(1deg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #ff6b6b;
    font-weight: 700;
    color: #495057;
    border-radius: 20px 20px 0 0 !important;
}

/* ===== CARTES DE STATISTIQUES CRÉATIVES ===== */
.card-stats {
    border-left: none;
    border-top: 6px solid var(--primary-color);
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, white 100%);
}

.card-stats.border-left-primary {
    border-top-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, white 100%);
}

.card-stats.border-left-success {
    border-top-color: var(--success-color);
    background: linear-gradient(135deg, rgba(81, 207, 102, 0.1) 0%, white 100%);
}

.card-stats.border-left-info {
    border-top-color: var(--info-color);
    background: linear-gradient(135deg, rgba(116, 192, 252, 0.1) 0%, white 100%);
}

.card-stats.border-left-warning {
    border-top-color: var(--warning-color);
    background: linear-gradient(135deg, rgba(255, 212, 59, 0.1) 0%, white 100%);
}

/* ===== BOUTONS CRÉATIFS ===== */
.btn {
    border-radius: 25px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
}

.btn-success {
    background: linear-gradient(135deg, #51cf66 0%, #69db7c 100%);
    box-shadow: 0 4px 15px rgba(81, 207, 102, 0.4);
}

.btn-info {
    background: linear-gradient(135deg, #74c0fc 0%, #91a7ff 100%);
    box-shadow: 0 4px 15px rgba(116, 192, 252, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #ffd43b 0%, #ffe066 100%);
    box-shadow: 0 4px 15px rgba(255, 212, 59, 0.4);
    color: #495057 !important;
}

/* ===== TABLEAUX CRÉATIFS ===== */
.table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: var(--gradient-primary);
    color: white;
    border: none;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
}

.table thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: #ffa8a8;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: linear-gradient(90deg, rgba(255, 107, 107, 0.1) 0%, white 100%);
    transform: scale(1.02);
}

.table tbody tr:nth-child(even) {
    background: rgba(248, 249, 250, 0.5);
}

/* ===== FORMULAIRES CRÉATIFS ===== */
.form-control, .form-select {
    border-radius: 15px;
    border: 2px solid #e9ecef;
    padding: 12px 20px;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
    transform: scale(1.05);
}

.form-label {
    font-weight: 700;
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

/* ===== BADGES CRÉATIFS ===== */
.badge {
    border-radius: 15px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 8px 15px;
}

.badge.bg-primary {
    background: var(--gradient-primary) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #51cf66, #69db7c) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #74c0fc, #91a7ff) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffd43b, #ffe066) !important;
    color: #495057 !important;
}

/* ===== CONTENU PRINCIPAL CRÉATIF ===== */
.main-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    position: relative;
    min-height: 100vh;
}

.main-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(78, 205, 196, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 212, 59, 0.1) 0%, transparent 50%);
}

.main-content > * {
    position: relative;
    z-index: 1;
}

/* ===== ANIMATIONS CRÉATIVES ===== */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0,-30px,0);
    }
    70% {
        transform: translate3d(0,-15px,0);
    }
    90% {
        transform: translate3d(0,-4px,0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.card-stats:hover {
    animation: pulse 0.6s ease-in-out;
}

/* ===== ALERTES CRÉATIVES ===== */
.alert {
    border-radius: 15px;
    border: none;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-primary {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 168, 168, 0.1));
    color: #c92a2a;
}

.alert-success {
    background: linear-gradient(135deg, rgba(81, 207, 102, 0.1), rgba(105, 219, 124, 0.1));
    color: #2b8a3e;
}

/* ===== RESPONSIVE CRÉATIF ===== */
@media (max-width: 768px) {
    .card:hover {
        transform: translateY(-5px);
    }
    
    .navbar-brand {
        font-size: 1.4rem;
    }
    
    .btn {
        border-radius: 20px;
    }
}
