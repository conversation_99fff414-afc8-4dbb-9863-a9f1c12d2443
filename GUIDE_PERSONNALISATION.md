# 🎨 Guide de Personnalisation - Application de Facturation

## 🌈 Thèmes Disponibles

Votre application dispose maintenant de **4 thèmes différents** que vous pouvez changer en temps réel :

### 1. 🔵 Thème par Défaut
- **Style :** Moderne et propre
- **Couleurs :** Bleu Bootstrap classique
- **Usage :** Polyvalent, adapté à tous les secteurs

### 2. 🌙 Thème Sombre
- **Style :** Mode sombre élégant
- **Couleurs :** Gris foncé et bleu
- **Usage :** Idéal pour le travail de nuit ou les environnements sombres

### 3. ✨ Thème Moderne
- **Style :** Gradients et effets visuels
- **Couleurs :** Violet et bleu avec gradients
- **Usage :** Pour une apparence moderne et dynamique

### 4. 🏢 Thème Corporate
- **Style :** Professionnel et sobre
- **Couleurs :** Bleu marine et orange
- **Usage :** Parfait pour les entreprises traditionnelles

## 🎛️ Comment Changer de Thème

### Méthode 1 : Sélecteur Visuel
1. Regardez à **droite de votre écran**
2. Vous verrez **4 boutons colorés** flottants
3. **Cliquez** sur celui qui vous plaît
4. Le thème change **instantanément**
5. Votre choix est **sauvegardé automatiquement**

### Méthode 2 : Modification du Code
Éditez le fichier `facturation/templates/facturation/base.html` ligne 17 :

```html
<!-- Pour le thème sombre -->
<link id="theme-css" href="{% static 'css/theme-dark.css' %}" rel="stylesheet">

<!-- Pour le thème moderne -->
<link id="theme-css" href="{% static 'css/theme-modern.css' %}" rel="stylesheet">

<!-- Pour le thème corporate -->
<link id="theme-css" href="{% static 'css/theme-corporate.css' %}" rel="stylesheet">
```

## 🛠️ Créer Votre Propre Thème

### Étape 1 : Créer un nouveau fichier CSS
```bash
# Créez un nouveau fichier dans static/css/
static/css/theme-monentreprise.css
```

### Étape 2 : Définir vos couleurs
```css
:root {
    --primary-color: #votre-couleur;
    --secondary-color: #votre-couleur;
    --success-color: #votre-couleur;
    --danger-color: #votre-couleur;
    --warning-color: #votre-couleur;
    --info-color: #votre-couleur;
    --light-color: #votre-couleur;
    --dark-color: #votre-couleur;
}
```

### Étape 3 : Personnaliser les éléments
```css
/* Navigation */
.navbar-dark {
    background-color: var(--primary-color) !important;
}

/* Cartes */
.card {
    border-color: var(--primary-color);
    /* Vos styles personnalisés */
}

/* Boutons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}
```

### Étape 4 : Ajouter au sélecteur
Modifiez `base.html` pour ajouter votre thème :

```html
<!-- Dans le CSS -->
.theme-monentreprise { background: linear-gradient(45deg, #votre-couleur1, #votre-couleur2); }

<!-- Dans le HTML -->
<div class="theme-btn theme-monentreprise" data-theme="monentreprise" title="Mon Thème"></div>

<!-- Dans le JavaScript -->
case 'monentreprise':
    cssFile = 'theme-monentreprise.css';
    break;
```

## 🎨 Éléments Personnalisables

### 🧭 Navigation
- **Couleur de fond :** `.navbar-dark`
- **Logo :** `.navbar-brand`
- **Liens :** `.nav-link`

### 📋 Cartes
- **Bordures :** `.card`
- **En-têtes :** `.card-header`
- **Statistiques :** `.card-stats`

### 🔘 Boutons
- **Primaire :** `.btn-primary`
- **Secondaire :** `.btn-secondary`
- **Succès :** `.btn-success`

### 📊 Tableaux
- **En-têtes :** `.table thead th`
- **Lignes :** `.table tbody tr`
- **Survol :** `.table tbody tr:hover`

### 📝 Formulaires
- **Champs :** `.form-control`
- **Labels :** `.form-label`
- **Focus :** `.form-control:focus`

## 🌟 Exemples de Personnalisation

### Thème "Entreprise Verte"
```css
:root {
    --primary-color: #27ae60;
    --secondary-color: #2ecc71;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}

.navbar-dark {
    background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
}

.card-stats {
    border-left: 4px solid #27ae60;
}
```

### Thème "Tech Startup"
```css
:root {
    --primary-color: #9b59b6;
    --secondary-color: #8e44ad;
    --accent-color: #e74c3c;
}

.navbar-dark {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
}

.btn-primary {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    border: none;
}
```

## 🖼️ Ajouter un Logo

### Étape 1 : Placer votre logo
```bash
# Placez votre logo dans :
static/images/logo.png
```

### Étape 2 : Modifier la navigation
```html
<!-- Dans base.html, remplacez -->
<i class="bi bi-receipt"></i> Facturation Pro

<!-- Par -->
<img src="{% static 'images/logo.png' %}" alt="Logo" height="30"> Votre Entreprise
```

## 🎭 Animations Personnalisées

### Ajouter des animations CSS
```css
/* Animation de fondu */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* Animation de survol */
.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
```

## 📱 Responsive Design

### Adapter pour mobile
```css
@media (max-width: 768px) {
    .theme-selector {
        right: 10px;
        top: auto;
        bottom: 20px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
}
```

## 🔧 Conseils de Personnalisation

### ✅ Bonnes Pratiques
- **Utilisez des variables CSS** pour faciliter les modifications
- **Testez sur différents écrans** (mobile, tablette, desktop)
- **Gardez une cohérence** dans les couleurs
- **Assurez-vous du contraste** pour l'accessibilité

### ❌ À Éviter
- Trop de couleurs différentes
- Animations trop lentes ou distrayantes
- Texte illisible sur fond coloré
- Modifications qui cassent la responsivité

## 🎯 Cas d'Usage Spécifiques

### Pour un Cabinet Comptable
```css
:root {
    --primary-color: #2c3e50;
    --accent-color: #f39c12;
}
/* Style sobre et professionnel */
```

### Pour une Agence Créative
```css
:root {
    --primary-color: #e74c3c;
    --secondary-color: #9b59b6;
}
/* Style coloré et dynamique */
```

### Pour une Entreprise Tech
```css
:root {
    --primary-color: #3498db;
    --accent-color: #2ecc71;
}
/* Style moderne et épuré */
```

## 🚀 Aller Plus Loin

### Intégration avec votre charte graphique
1. **Récupérez vos couleurs** d'entreprise
2. **Adaptez les variables CSS**
3. **Ajoutez votre logo**
4. **Testez sur tous les écrans**
5. **Formez vos utilisateurs**

Votre application peut maintenant refléter parfaitement l'identité visuelle de votre entreprise ! 🎨
