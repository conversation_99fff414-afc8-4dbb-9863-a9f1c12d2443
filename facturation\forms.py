from django import forms
from django.forms import inlineformset_factory
from .models import Document, LigneDocument, Client, Produit
from datetime import date, timedelta


class DocumentForm(forms.ModelForm):
    class Meta:
        model = Document
        fields = ['numero', 'client', 'date_document', 'date_echeance', 'statut', 'notes']
        widgets = {
            'date_document': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'date_echeance': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'numero': forms.TextInput(attrs={'class': 'form-control'}),
            'client': forms.Select(attrs={'class': 'form-control'}),
            'statut': forms.Select(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Valeurs par défaut
        if not self.instance.pk:
            self.fields['date_document'].initial = date.today()
            self.fields['date_echeance'].initial = date.today() + timedelta(days=30)


class LigneDocumentForm(forms.ModelForm):
    class Meta:
        model = LigneDocument
        fields = ['produit', 'description', 'quantite', 'prix_unitaire', 'tva_taux']
        widgets = {
            'produit': forms.Select(attrs={'class': 'form-control produit-select'}),
            'description': forms.TextInput(attrs={'class': 'form-control'}),
            'quantite': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'prix_unitaire': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'tva_taux': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filtrer les produits actifs
        self.fields['produit'].queryset = Produit.objects.filter(actif=True)
        
        # Pré-remplir avec les données du produit si sélectionné
        if self.instance.pk and self.instance.produit:
            produit = self.instance.produit
            if not self.instance.description:
                self.fields['description'].initial = produit.description
            if not self.instance.prix_unitaire:
                self.fields['prix_unitaire'].initial = produit.prix_unitaire
            if not self.instance.tva_taux:
                self.fields['tva_taux'].initial = produit.tva_taux


# Formset pour les lignes de document
LigneDocumentFormSet = inlineformset_factory(
    Document,
    LigneDocument,
    form=LigneDocumentForm,
    extra=3,
    can_delete=True,
    min_num=1,
    validate_min=True
)


class ClientForm(forms.ModelForm):
    class Meta:
        model = Client
        fields = ['nom', 'prenom', 'entreprise', 'email', 'telephone', 'adresse', 'ville', 'code_postal', 'pays']
        widgets = {
            'nom': forms.TextInput(attrs={'class': 'form-control'}),
            'prenom': forms.TextInput(attrs={'class': 'form-control'}),
            'entreprise': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'telephone': forms.TextInput(attrs={'class': 'form-control'}),
            'adresse': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'ville': forms.TextInput(attrs={'class': 'form-control'}),
            'code_postal': forms.TextInput(attrs={'class': 'form-control'}),
            'pays': forms.TextInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Valeur par défaut pour le pays
        if not self.instance.pk:
            self.fields['pays'].initial = 'France'


class ProduitForm(forms.ModelForm):
    class Meta:
        model = Produit
        fields = ['nom', 'description', 'prix_unitaire', 'unite', 'tva_taux', 'actif']
        widgets = {
            'nom': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'prix_unitaire': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'unite': forms.TextInput(attrs={'class': 'form-control'}),
            'tva_taux': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'actif': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Valeurs par défaut
        if not self.instance.pk:
            self.fields['unite'].initial = 'unité'
            self.fields['tva_taux'].initial = 20.00
            self.fields['actif'].initial = True


class RechercheForm(forms.Form):
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Rechercher...'
        })
    )
    
    type_document = forms.ChoiceField(
        choices=[('', 'Tous les types')] + Document.TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    statut = forms.ChoiceField(
        choices=[('', 'Tous les statuts')] + Document.STATUT_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
