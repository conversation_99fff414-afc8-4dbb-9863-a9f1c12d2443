from django.urls import path
from . import views

app_name = 'facturation'

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='dashboard'),
    
    # Documents
    path('documents/', views.liste_documents, name='liste_documents'),
    path('documents/<int:pk>/', views.detail_document, name='detail_document'),
    path('documents/<int:pk>/modifier/', views.modifier_document, name='modifier_document'),
    path('documents/<int:pk>/pdf/', views.telecharger_pdf, name='telecharger_pdf'),
    
    # Création de documents
    path('facture/creer/', views.creer_document, {'type_doc': 'facture'}, name='creer_facture'),
    path('devis/creer/', views.creer_document, {'type_doc': 'devis'}, name='creer_devis'),
    path('bon-livraison/creer/', views.creer_document, {'type_doc': 'bon_livraison'}, name='creer_bon_livraison'),
    
    # Clients
    path('clients/', views.liste_clients, name='liste_clients'),
    path('clients/creer/', views.creer_client, name='creer_client'),
    
    # Produits
    path('produits/', views.liste_produits, name='liste_produits'),
    path('produits/creer/', views.creer_produit, name='creer_produit'),
]
