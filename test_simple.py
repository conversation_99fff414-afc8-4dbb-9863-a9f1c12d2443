#!/usr/bin/env python
"""
Test simple pour diagnostiquer les problèmes
"""
import sys
import os

def test_python():
    """Test de Python"""
    print(f"✅ Python version: {sys.version}")
    return True

def test_django():
    """Test de Django"""
    try:
        import django
        print(f"✅ Django version: {django.get_version()}")
        return True
    except ImportError:
        print("❌ Django non installé")
        return False

def test_reportlab():
    """Test de ReportLab"""
    try:
        import reportlab
        print(f"✅ ReportLab installé")
        return True
    except ImportError:
        print("❌ ReportLab non installé")
        return False

def test_pillow():
    """Test de Pillow"""
    try:
        import PIL
        print(f"✅ Pillow installé")
        return True
    except ImportError:
        print("❌ Pillow non installé")
        return False

def test_files():
    """Test des fichiers"""
    required_files = [
        'manage.py',
        'facturation/models.py',
        'facturation/views.py',
        'projet_facturation/settings.py'
    ]
    
    all_ok = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} manquant")
            all_ok = False
    
    return all_ok

def test_django_setup():
    """Test de la configuration Django"""
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'projet_facturation.settings')
        import django
        django.setup()
        print("✅ Configuration Django OK")
        return True
    except Exception as e:
        print(f"❌ Erreur Django: {e}")
        return False

def main():
    """Test principal"""
    print("=== Diagnostic de l'application ===")
    print()
    
    tests = [
        ("Python", test_python),
        ("Django", test_django),
        ("ReportLab", test_reportlab),
        ("Pillow", test_pillow),
        ("Fichiers", test_files),
        ("Configuration Django", test_django_setup),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"Test {name}...")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Erreur lors du test {name}: {e}")
            results.append(False)
        print()
    
    # Résumé
    print("=== Résumé ===")
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print("🎉 Tous les tests sont passés!")
        print("Vous pouvez maintenant exécuter:")
        print("  python manage.py runserver")
    else:
        print(f"⚠️  {success_count}/{total_count} tests réussis")
        print("Consultez le GUIDE_DEPANNAGE.md pour résoudre les problèmes")

if __name__ == '__main__':
    main()
