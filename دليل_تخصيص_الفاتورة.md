# 🧾 دليل تخصيص الفاتورة - تصميم احترافي وجذاب

## 🎨 التحسينات المضافة للفاتورة

### ✅ ما تم تطويره:

**1. 🏢 رأس الفاتورة المحسن:**
- إضافة مكان للوجو (Logo) 
- تنسيق معلومات الشركة بشكل جذاب
- خط فاصل ملون أنيق
- ألوان متناسقة ومهنية

**2. 📋 معلومات العميل والوثيقة:**
- تصميم ثنائي اللغة (عربي/فرنسي)
- جداول منسقة مع خلفيات ملونة
- تنظيم أفضل للمعلومات
- ألوان مميزة للمعلومات المهمة

**3. 📊 جدول المنتجات:**
- رؤوس أعمدة ثنائية اللغة
- ألوان متدرجة للصفوف
- حدود وتباعد محسن
- خط أوضح وأكبر

**4. 💰 قسم المجاميع:**
- تصميم بارز للمجموع النهائي
- ألوان مميزة للمبالغ
- تنسيق ثنائي اللغة
- إطار ملون للمجموع الإجمالي

**5. 📝 التذييل:**
- معلومات إنشاء الوثيقة
- رسالة شكر ثنائية اللغة
- تاريخ ووقت الإنشاء

## 🖼️ كيفية إضافة اللوجو

### الطريقة 1: عبر واجهة الإدارة

1. **ادخل إلى الإدارة:** http://127.0.0.1:8000/admin/
2. **اذهب إلى:** Entreprises
3. **اختر شركتك** أو أنشئ واحدة جديدة
4. **في حقل "Logo"** ارفع صورة اللوجو
5. **احفظ التغييرات**

### الطريقة 2: إضافة اللوجو يدوياً

```bash
# ضع ملف اللوجو في:
media/logos/logo-شركتي.png
```

### متطلبات اللوجو:
- **الصيغة:** PNG, JPG, أو GIF
- **الحجم المفضل:** 300x200 بكسل
- **الحجم الأقصى:** 2 ميجابايت
- **الشفافية:** PNG مع خلفية شفافة (مفضل)

## 🎨 تخصيص الألوان

### تغيير الألوان الأساسية:

في ملف `facturation/utils.py`، يمكنك تغيير الألوان:

```python
# ألوان الرأس والعناوين
colors.HexColor('#2c3e50')  # أزرق داكن
colors.HexColor('#3498db')  # أزرق فاتح
colors.HexColor('#e74c3c')  # أحمر للمبالغ المهمة
colors.HexColor('#34495e')  # رمادي للنصوص
```

### ألوان مقترحة حسب النشاط:

**🏥 للمجال الطبي:**
```python
colors.HexColor('#27ae60')  # أخضر طبي
colors.HexColor('#2ecc71')  # أخضر فاتح
```

**⚖️ للمجال القانوني:**
```python
colors.HexColor('#2c3e50')  # أزرق داكن
colors.HexColor('#f39c12')  # ذهبي
```

**🎨 للمجال الإبداعي:**
```python
colors.HexColor('#e74c3c')  # أحمر
colors.HexColor('#9b59b6')  # بنفسجي
```

**🏭 للمجال الصناعي:**
```python
colors.HexColor('#7f8c8d')  # رمادي معدني
colors.HexColor('#e67e22')  # برتقالي
```

## 📝 تخصيص النصوص

### إضافة معلومات إضافية:

```python
# في قسم معلومات الشركة
company_info += f'<font size="10" color="#34495e">الرقم الضريبي: {entreprise.tax_number}</font><br/>'
company_info += f'<font size="10" color="#34495e">رقم السجل التجاري: {entreprise.commercial_register}</font><br/>'
```

### تخصيص رسالة الشكر:

```python
footer_text = f"""
<font size="8" color="#7f8c8d">
شكراً لاختياركم خدماتنا<br/>
Merci d'avoir choisi nos services<br/>
للاستفسارات: {entreprise.telephone} | {entreprise.email}
</font>
"""
```

## 🌍 دعم اللغات

### إضافة لغات أخرى:

```python
# للإنجليزية
'الوصف / Description / Description'

# للألمانية  
'الوصف / Description / Beschreibung'

# للإسبانية
'الوصف / Description / Descripción'
```

## 🎯 تخصيصات متقدمة

### إضافة رمز QR:

```python
from reportlab.graphics.barcode.qr import QrCodeWidget
from reportlab.graphics.shapes import Drawing

# إنشاء رمز QR
qr_code = QrCodeWidget(f"Invoice: {document.numero}")
qr_drawing = Drawing(2*cm, 2*cm)
qr_drawing.add(qr_code)
```

### إضافة خط مائي:

```python
# إضافة نص خلفي للفاتورة
watermark = Paragraph(
    '<font size="50" color="#f0f0f0">PAID</font>',
    ParagraphStyle('Watermark', alignment=TA_CENTER)
)
```

### تخصيص الخطوط:

```python
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# تسجيل خط عربي
pdfmetrics.registerFont(TTFont('Arabic', 'fonts/arabic-font.ttf'))

# استخدام الخط
fontName='Arabic'
```

## 📊 تخصيص التخطيط

### تغيير حجم الصفحة:

```python
from reportlab.lib.pagesizes import letter, legal

# استخدام حجم Letter الأمريكي
doc = SimpleDocTemplate(filepath, pagesize=letter)

# أو حجم Legal
doc = SimpleDocTemplate(filepath, pagesize=legal)
```

### تخصيص الهوامش:

```python
doc = SimpleDocTemplate(
    filepath, 
    pagesize=A4, 
    topMargin=1*cm,     # هامش علوي أصغر
    bottomMargin=3*cm,  # هامش سفلي أكبر
    leftMargin=1.5*cm,  # هامش أيسر
    rightMargin=1.5*cm  # هامش أيمن
)
```

## 🎨 أمثلة تصاميم جاهزة

### تصميم كلاسيكي:
- ألوان: أزرق داكن وذهبي
- خط: Helvetica
- تخطيط: تقليدي ومحافظ

### تصميم عصري:
- ألوان: متدرجة وزاهية  
- خط: عصري
- تخطيط: ديناميكي

### تصميم مينيمال:
- ألوان: رمادي وأبيض
- خط: بسيط
- تخطيط: نظيف ومرتب

## 🔧 نصائح للتحسين

### ✅ أفضل الممارسات:
- **استخدم ألوان متناسقة** مع هوية شركتك
- **اجعل اللوجو واضح** وبجودة عالية
- **اختبر الطباعة** بالأبيض والأسود
- **تأكد من وضوح النصوص** في جميع الأحجام

### ❌ تجنب:
- الألوان الزاهية جداً
- النصوص الصغيرة جداً
- الصور منخفضة الجودة
- التصاميم المعقدة جداً

## 🚀 تطبيق التغييرات

بعد إجراء أي تعديلات:

1. **احفظ الملفات**
2. **أعد تشغيل الخادم** إذا لزم الأمر
3. **أنشئ فاتورة جديدة** لاختبار التغييرات
4. **تحقق من النتيجة** في ملف PDF

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من صيغة ملف اللوجو
2. تأكد من مسار الملف صحيح
3. راجع رسائل الخطأ في وحدة التحكم
4. اختبر مع لوجو أصغر حجماً

الآن فاتورتك ستبدو احترافية وجذابة! 🎉
