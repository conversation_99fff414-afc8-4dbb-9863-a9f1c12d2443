{% extends 'facturation/base.html' %}

{% block title %}{{ title }} - Facturation Pro{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'facturation:liste_clients' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Retour à la liste
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Informations du client</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.nom.id_for_label }}" class="form-label">Nom *</label>
                                {{ form.nom }}
                                {% if form.nom.errors %}
                                    <div class="text-danger">{{ form.nom.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.prenom.id_for_label }}" class="form-label">Prénom</label>
                                {{ form.prenom }}
                                {% if form.prenom.errors %}
                                    <div class="text-danger">{{ form.prenom.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.entreprise.id_for_label }}" class="form-label">Entreprise</label>
                        {{ form.entreprise }}
                        {% if form.entreprise.errors %}
                            <div class="text-danger">{{ form.entreprise.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">Email *</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger">{{ form.email.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.telephone.id_for_label }}" class="form-label">Téléphone</label>
                                {{ form.telephone }}
                                {% if form.telephone.errors %}
                                    <div class="text-danger">{{ form.telephone.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.adresse.id_for_label }}" class="form-label">Adresse *</label>
                        {{ form.adresse }}
                        {% if form.adresse.errors %}
                            <div class="text-danger">{{ form.adresse.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.code_postal.id_for_label }}" class="form-label">Code postal *</label>
                                {{ form.code_postal }}
                                {% if form.code_postal.errors %}
                                    <div class="text-danger">{{ form.code_postal.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.ville.id_for_label }}" class="form-label">Ville *</label>
                                {{ form.ville }}
                                {% if form.ville.errors %}
                                    <div class="text-danger">{{ form.ville.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.pays.id_for_label }}" class="form-label">Pays *</label>
                                {{ form.pays }}
                                {% if form.pays.errors %}
                                    <div class="text-danger">{{ form.pays.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% url 'facturation:liste_clients' %}" class="btn btn-secondary me-2">
                            <i class="bi bi-x"></i> Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save"></i> Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Aide</h5>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-info-circle"></i> Les champs marqués d'un * sont obligatoires</li>
                        <li><i class="bi bi-info-circle"></i> L'email sera utilisé pour l'envoi des documents</li>
                        <li><i class="bi bi-info-circle"></i> Vous pourrez modifier ces informations plus tard</li>
                    </ul>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
