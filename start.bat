@echo off
echo === Initialisation du projet de facturation ===
echo.

echo Installation des dependances...
pip install django reportlab pillow

echo.
echo Creation des migrations...
python manage.py makemigrations

echo.
echo Application des migrations...
python manage.py migrate

echo.
echo Creation du superutilisateur...
echo from django.contrib.auth.models import User; User.objects.create_superuser('admin', '<EMAIL>', 'admin123') if not User.objects.filter(username='admin').exists() else None | python manage.py shell

echo.
echo === Initialisation terminee ===
echo.
echo Vous pouvez maintenant:
echo 1. Lance<PERSON> le serveur: python manage.py runserver
echo 2. Acceder a l'admin: http://127.0.0.1:8000/admin/
echo 3. Acceder a l'application: http://127.0.0.1:8000/
echo 4. Identifiants admin: admin / admin123
echo.
pause
