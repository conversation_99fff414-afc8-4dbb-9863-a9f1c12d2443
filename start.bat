@echo off
echo === Initialisation du projet de facturation ===
echo.

echo Etape 1: Installation des dependances...
pip install django reportlab pillow
if %errorlevel% neq 0 (
    echo ERREUR: Installation des dependances echouee
    pause
    exit /b 1
)

echo.
echo Etape 2: Creation des migrations...
python manage.py makemigrations facturation
if %errorlevel% neq 0 (
    echo ERREUR: Creation des migrations echouee
    pause
    exit /b 1
)

echo.
echo Etape 3: Application des migrations...
python manage.py migrate
if %errorlevel% neq 0 (
    echo ERREUR: Application des migrations echouee
    pause
    exit /b 1
)

echo.
echo Etape 4: Creation du superutilisateur...
python manage.py shell -c "from django.contrib.auth.models import User; User.objects.create_superuser('admin', '<EMAIL>', 'admin123') if not User.objects.filter(username='admin').exists() else print('Utilisateur admin existe deja')"

echo.
echo Etape 5: Creation des donnees d'exemple...
python manage.py init_data

echo.
echo === Initialisation terminee avec succes! ===
echo.
echo Pour demarrer l'application:
echo   python manage.py runserver
echo.
echo Puis ouvrez votre navigateur sur:
echo   http://127.0.0.1:8000/        (Application)
echo   http://127.0.0.1:8000/admin/  (Administration)
echo.
echo Identifiants admin: admin / admin123
echo.
pause
