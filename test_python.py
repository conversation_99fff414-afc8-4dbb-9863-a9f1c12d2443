print("=== Test Python ===")
print("Python fonctionne correctement!")
print()

import sys
print(f"Version Python: {sys.version}")
print()

try:
    import django
    print(f"✅ Django version: {django.get_version()}")
except ImportError:
    print("❌ Django non installé - Exécutez: pip install django")

try:
    import reportlab
    print("✅ ReportLab installé")
except ImportError:
    print("❌ ReportLab non installé - Exécutez: pip install reportlab")

try:
    import PIL
    print("✅ Pillow installé")
except ImportError:
    print("❌ Pillow non installé - Exécutez: pip install pillow")

print()
print("Si vous voyez des ❌, installez les packages manquants avec pip install")
input("Appuyez sur Entrée pour continuer...")
