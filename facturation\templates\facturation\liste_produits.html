{% extends 'facturation/base.html' %}

{% block title %}Produits - Facturation Pro{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Produits</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'facturation:creer_produit' %}" class="btn btn-primary">
            <i class="bi bi-plus"></i> Nouveau produit
        </a>
    </div>
</div>

<!-- Recherche -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-8">
                <label for="search" class="form-label">Recherche</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ current_search }}" placeholder="Nom, description...">
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="bi bi-search"></i> Rechercher
                </button>
                <a href="{% url 'facturation:liste_produits' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-x"></i> Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Liste des produits -->
<div class="card">
    <div class="card-body">
        {% if produits %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Description</th>
                            <th>Prix unitaire</th>
                            <th>Unité</th>
                            <th>TVA</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for produit in produits %}
                        <tr>
                            <td><strong>{{ produit.nom }}</strong></td>
                            <td>
                                {% if produit.description %}
                                    {{ produit.description|truncatechars:50 }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td><strong>{{ produit.prix_unitaire|floatformat:2 }}€</strong></td>
                            <td>{{ produit.unite }}</td>
                            <td>{{ produit.tva_taux|floatformat:1 }}%</td>
                            <td>
                                {% if produit.actif %}
                                    <span class="badge bg-success">Actif</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactif</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="#" class="btn btn-outline-primary" title="Voir">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-outline-secondary" title="Modifier">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-box display-1 text-muted"></i>
                <h4 class="mt-3">Aucun produit trouvé</h4>
                <p class="text-muted">Commencez par ajouter votre premier produit.</p>
                <a href="{% url 'facturation:creer_produit' %}" class="btn btn-primary">
                    <i class="bi bi-plus"></i> Créer un produit
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
