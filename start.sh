#!/bin/bash

echo "=== Initialisation du projet de facturation ==="
echo

echo "Installation des dépendances..."
pip install django reportlab pillow

echo
echo "Création des migrations..."
python manage.py makemigrations

echo
echo "Application des migrations..."
python manage.py migrate

echo
echo "Création du superutilisateur..."
echo "from django.contrib.auth.models import User; User.objects.create_superuser('admin', '<EMAIL>', 'admin123') if not User.objects.filter(username='admin').exists() else None" | python manage.py shell

echo
echo "=== Initialisation terminée ==="
echo
echo "Vous pouvez maintenant:"
echo "1. Lancer le serveur: python manage.py runserver"
echo "2. Accéder à l'admin: http://127.0.0.1:8000/admin/"
echo "3. Accéder à l'application: http://127.0.0.1:8000/"
echo "4. Identifiants admin: admin / admin123"
echo
