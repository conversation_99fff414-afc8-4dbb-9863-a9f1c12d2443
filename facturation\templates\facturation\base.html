<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Système de Facturation{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">

    <!-- CSS Personnalisés -->
    {% load static %}
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">

    <!-- Thème sélectionnable -->
    <link id="theme-css" href="{% static 'css/custom.css' %}" rel="stylesheet">

    <style>
        /* Styles de base conservés pour compatibilité */
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: 100vh;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card-stats {
            border-left: 4px solid #007bff;
        }

        /* Sélecteur de thème */
        .theme-selector {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            z-index: 1050;
            background: white;
            border-radius: 50px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 10px;
        }

        .theme-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid white;
            margin: 5px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
        }

        .theme-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .theme-btn.active {
            border-color: #007bff;
            transform: scale(1.2);
        }

        .theme-default { background: linear-gradient(45deg, #007bff, #6c757d); }
        .theme-dark { background: linear-gradient(45deg, #1a202c, #2d3748); }
        .theme-modern { background: linear-gradient(45deg, #667eea, #764ba2); }
        .theme-corporate { background: linear-gradient(45deg, #2c3e50, #f39c12); }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'facturation:dashboard' %}">
                <i class="bi bi-receipt"></i> Facturation Pro
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'facturation:dashboard' %}">
                            <i class="bi bi-house"></i> Accueil
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="documentsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-file-text"></i> Documents
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'facturation:liste_documents' %}">Tous les documents</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'facturation:creer_facture' %}">Nouvelle facture</a></li>
                            <li><a class="dropdown-item" href="{% url 'facturation:creer_devis' %}">Nouveau devis</a></li>
                            <li><a class="dropdown-item" href="{% url 'facturation:creer_bon_livraison' %}">Nouveau bon de livraison</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'facturation:liste_clients' %}">
                            <i class="bi bi-people"></i> Clients
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'facturation:liste_produits' %}">
                            <i class="bi bi-box"></i> Produits
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/">
                            <i class="bi bi-gear"></i> Administration
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="container-fluid mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Contenu principal -->
    <div class="container-fluid">
        <div class="row">
            <main class="col-12 main-content p-4">
                {% block content %}
                {% endblock %}
            </main>
        </div>
    </div>

    <!-- Sélecteur de thème -->
    <div class="theme-selector">
        <div class="theme-btn theme-default active" data-theme="default" title="Thème par défaut"></div>
        <div class="theme-btn theme-dark" data-theme="dark" title="Thème sombre"></div>
        <div class="theme-btn theme-modern" data-theme="modern" title="Thème moderne"></div>
        <div class="theme-btn theme-corporate" data-theme="corporate" title="Thème corporate"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Script de changement de thème -->
    <script>
        // Gestion des thèmes
        const themeButtons = document.querySelectorAll('.theme-btn');
        const themeCSS = document.getElementById('theme-css');

        // Charger le thème sauvegardé
        const savedTheme = localStorage.getItem('selectedTheme') || 'default';
        loadTheme(savedTheme);

        // Ajouter les événements de clic
        themeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const theme = btn.dataset.theme;
                loadTheme(theme);
                localStorage.setItem('selectedTheme', theme);
            });
        });

        function loadTheme(theme) {
            // Mettre à jour le CSS
            const basePath = "{% static 'css/' %}";
            let cssFile = 'custom.css';

            switch(theme) {
                case 'dark':
                    cssFile = 'theme-dark.css';
                    break;
                case 'modern':
                    cssFile = 'theme-modern.css';
                    break;
                case 'corporate':
                    cssFile = 'theme-corporate.css';
                    break;
                default:
                    cssFile = 'custom.css';
            }

            themeCSS.href = basePath + cssFile;

            // Mettre à jour les boutons actifs
            themeButtons.forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.theme === theme) {
                    btn.classList.add('active');
                }
            });

            // Ajouter une classe au body pour les thèmes spéciaux
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            if (theme !== 'default') {
                document.body.classList.add('theme-' + theme);
            }
        }

        // Animation d'entrée pour les cartes
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>

    {% block extra_js %}
    {% endblock %}
</body>
</html>
