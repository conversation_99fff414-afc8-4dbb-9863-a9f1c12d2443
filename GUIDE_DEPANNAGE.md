# Guide de Dépannage - Application de Facturation

## 🚨 Problèmes Courants et Solutions

### 1. L'application ne démarre pas

#### Vérifiez Python
```bash
python --version
```
**Solution :** Assurez-vous d'avoir Python 3.8+ installé

#### Vérifiez les dépendances
```bash
pip list
```
**Solution :** Installez les dépendances manquantes :
```bash
pip install django reportlab pillow
```

### 2. Erreur "No module named 'django'"

**Cause :** Django n'est pas installé
**Solution :**
```bash
pip install django
```

### 3. Erreur de migration

**Cause :** Base de données non initialisée
**Solution :**
```bash
python manage.py makemigrations facturation
python manage.py migrate
```

### 4. Erreur "Table doesn't exist"

**Solution :** Recréer la base de données
```bash
del db.sqlite3
python manage.py makemigrations facturation
python manage.py migrate
```

### 5. Impossible d'accéder à l'admin

**Cause :** Pas de superutilisateur
**Solution :**
```bash
python manage.py createsuperuser
```

## 🔧 Installation Manuelle Étape par Étape

### Étape 1 : Vérifier l'environnement
```bash
# Vérifier Python
python --version

# Vérifier pip
pip --version
```

### Étape 2 : Installer les dépendances
```bash
pip install django==5.2.1
pip install reportlab==4.4.1
pip install pillow==11.2.1
```

### Étape 3 : Créer la base de données
```bash
python manage.py makemigrations facturation
python manage.py migrate
```

### Étape 4 : Créer un administrateur
```bash
python manage.py createsuperuser
```
- Username: admin
- Email: <EMAIL>
- Password: admin123

### Étape 5 : Ajouter des données d'exemple
```bash
python manage.py init_data
```

### Étape 6 : Démarrer le serveur
```bash
python manage.py runserver
```

## 🌐 Test de l'Application

### URLs à tester :
- **Application principale :** http://127.0.0.1:8000/
- **Administration :** http://127.0.0.1:8000/admin/
- **Dashboard :** http://127.0.0.1:8000/

### Fonctionnalités à tester :
1. ✅ Connexion à l'admin
2. ✅ Création d'un client
3. ✅ Création d'un produit
4. ✅ Création d'une facture
5. ✅ Génération du PDF

## 🐛 Erreurs Spécifiques

### Erreur ReportLab
```
ModuleNotFoundError: No module named 'reportlab'
```
**Solution :**
```bash
pip install reportlab
```

### Erreur Pillow
```
ModuleNotFoundError: No module named 'PIL'
```
**Solution :**
```bash
pip install pillow
```

### Erreur de permissions (PDF)
```
PermissionError: [Errno 13] Permission denied
```
**Solution :** Créer le dossier media
```bash
mkdir media
mkdir media\pdfs
```

### Erreur CSRF
```
Forbidden (403) CSRF verification failed
```
**Solution :** Vérifier que `{% csrf_token %}` est dans les formulaires

## 🔍 Diagnostic Avancé

### Vérifier les logs Django
```bash
python manage.py runserver --verbosity=2
```

### Tester les modèles
```bash
python manage.py shell
```
```python
from facturation.models import Client, Produit
print(Client.objects.count())
print(Produit.objects.count())
```

### Vérifier les templates
```bash
python manage.py check --deploy
```

## 📞 Support

Si les problèmes persistent :

1. **Vérifiez les versions :**
   - Python 3.8+
   - Django 5.0+
   - ReportLab 4.0+

2. **Recréez l'environnement :**
   ```bash
   pip uninstall django reportlab pillow
   pip install django reportlab pillow
   ```

3. **Réinitialisez la base :**
   ```bash
   del db.sqlite3
   python manage.py migrate
   ```

## 🎯 Checklist de Vérification

- [ ] Python installé et fonctionnel
- [ ] Dépendances installées (django, reportlab, pillow)
- [ ] Migrations créées et appliquées
- [ ] Superutilisateur créé
- [ ] Serveur démarre sans erreur
- [ ] Pages accessibles dans le navigateur
- [ ] Admin accessible
- [ ] Génération PDF fonctionne

## 🚀 Démarrage Rapide

Si tout échoue, utilisez cette séquence :

```bash
# 1. Nettoyer
del db.sqlite3

# 2. Réinstaller
pip install --force-reinstall django reportlab pillow

# 3. Recréer
python manage.py makemigrations facturation
python manage.py migrate
python manage.py createsuperuser

# 4. Démarrer
python manage.py runserver
```
