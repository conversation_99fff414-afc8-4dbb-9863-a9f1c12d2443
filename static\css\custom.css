/* ===== STYLES PERSONNALISÉS POUR L'APPLICATION DE FACTURATION ===== */

/* Variables CSS pour faciliter la personnalisation */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --navbar-bg: var(--primary-color);
    --sidebar-bg: var(--light-color);
    --card-border-radius: 0.5rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* ===== NAVIGATION ===== */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    margin-right: 0.5rem;
    font-size: 1.3rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* ===== CARTES ET CONTENEURS ===== */
.card {
    border-radius: var(--card-border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(0, 0, 0, 0.125);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* ===== CARTES DE STATISTIQUES ===== */
.card-stats {
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.card-stats:hover {
    border-left-width: 6px;
}

.card-stats.border-left-primary {
    border-left-color: var(--primary-color);
}

.card-stats.border-left-success {
    border-left-color: var(--success-color);
}

.card-stats.border-left-info {
    border-left-color: var(--info-color);
}

.card-stats.border-left-warning {
    border-left-color: var(--warning-color);
}

/* ===== BOUTONS ===== */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* ===== TABLEAUX ===== */
.table {
    border-radius: var(--card-border-radius);
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: scale(1.01);
}

/* ===== BADGES ===== */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* ===== FORMULAIRES ===== */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: scale(1.02);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* ===== ALERTES ===== */
.alert {
    border-radius: var(--card-border-radius);
    border: none;
    box-shadow: var(--box-shadow);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* ===== CONTENU PRINCIPAL ===== */
.main-content {
    min-height: 100vh;
    background-color: #f8f9fa;
}

/* ===== SIDEBAR (si utilisée) ===== */
.sidebar {
    min-height: 100vh;
    background-color: var(--sidebar-bg);
    border-right: 1px solid rgba(0, 0, 0, 0.125);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .card:hover {
        transform: none;
    }
    
    .table tbody tr:hover {
        transform: none;
    }
    
    .btn:hover {
        transform: none;
    }
}

/* ===== UTILITAIRES ===== */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.border-radius-custom {
    border-radius: var(--card-border-radius);
}
