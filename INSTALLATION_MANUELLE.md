# Installation Manuelle - Application de Facturation

## 🚀 Installation Étape par Étape

### Prérequis
- Python 3.8+ installé sur votre système
- Accès à Internet pour télécharger les packages

### Étape 1 : Ouvrir un terminal/invite de commandes

**Windows :** 
- A<PERSON><PERSON><PERSON> sur `Win + R`, tapez `cmd`, puis Entrée
- Ou recherchez "Invite de commandes" dans le menu Démarrer

**Mac/Linux :**
- Ouvrez le Terminal

### Étape 2 : Naviguer vers le dossier du projet

```bash
cd "C:\Users\<USER>\Documents\augment-projects\FCBL"
```

### Étape 3 : Installer les dépendances

Exécutez ces commandes **une par une** :

```bash
pip install django
```
Attendez que l'installation se termine, puis :

```bash
pip install reportlab
```
Attendez que l'installation se termine, puis :

```bash
pip install pillow
```

### Étape 4 : Vérifier les installations

```bash
python test_simple.py
```

Vous devriez voir tous les tests passer ✅

### Étape 5 : Créer la base de données

```bash
python manage.py makemigrations facturation
```

Puis :

```bash
python manage.py migrate
```

### Étape 6 : Créer un administrateur

```bash
python manage.py createsuperuser
```

Entrez :
- **Username :** admin
- **Email :** <EMAIL>
- **Password :** admin123
- **Confirmer password :** admin123

### Étape 7 : Ajouter des données d'exemple (optionnel)

```bash
python manage.py init_data
```

### Étape 8 : Démarrer le serveur

```bash
python manage.py runserver
```

### Étape 9 : Ouvrir l'application

Ouvrez votre navigateur et allez sur :
- **Application :** http://127.0.0.1:8000/
- **Administration :** http://127.0.0.1:8000/admin/

## 🔧 Si ça ne marche toujours pas

### Problème : "pip n'est pas reconnu"

**Solution :** Réinstallez Python en cochant "Add to PATH"

### Problème : "python n'est pas reconnu"

**Solution :** Ajoutez Python au PATH ou utilisez `py` au lieu de `python`

### Problème : Erreur de permissions

**Solution :** Exécutez l'invite de commandes en tant qu'administrateur

### Problème : Erreur réseau

**Solution :** Vérifiez votre connexion Internet ou utilisez :
```bash
pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org django reportlab pillow
```

## 📱 Test Final

Une fois tout installé, testez ces URLs :

1. **Dashboard :** http://127.0.0.1:8000/
2. **Admin :** http://127.0.0.1:8000/admin/ (admin/admin123)
3. **Créer une facture :** http://127.0.0.1:8000/facture/creer/

## 🎯 Fonctionnalités à Tester

1. ✅ Connexion à l'admin
2. ✅ Ajouter un client
3. ✅ Ajouter un produit  
4. ✅ Créer une facture
5. ✅ Télécharger le PDF

## 📞 Aide Supplémentaire

Si vous avez encore des problèmes :

1. **Vérifiez votre version de Python :**
   ```bash
   python --version
   ```
   Doit être 3.8 ou plus récent

2. **Réinstallez tout :**
   ```bash
   pip uninstall django reportlab pillow
   pip install django reportlab pillow
   ```

3. **Utilisez un environnement virtuel :**
   ```bash
   python -m venv venv
   venv\Scripts\activate
   pip install django reportlab pillow
   ```

## 🚨 Commandes de Dépannage

```bash
# Vérifier Python
python --version

# Vérifier pip
pip --version

# Lister les packages installés
pip list

# Réinstaller un package
pip install --force-reinstall django

# Nettoyer et recommencer
del db.sqlite3
python manage.py makemigrations facturation
python manage.py migrate
```

## ✅ Checklist Finale

- [ ] Python 3.8+ installé
- [ ] pip fonctionne
- [ ] django installé
- [ ] reportlab installé  
- [ ] pillow installé
- [ ] Migrations créées
- [ ] Base de données migrée
- [ ] Superutilisateur créé
- [ ] Serveur démarre
- [ ] Application accessible dans le navigateur
