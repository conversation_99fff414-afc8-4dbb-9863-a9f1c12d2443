#!/usr/bin/env python
"""
Script d'installation des dépendances
"""
import subprocess
import sys
import os

def install_package(package):
    """Installe un package avec pip"""
    try:
        print(f"Installation de {package}...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package} installé avec succès")
            return True
        else:
            print(f"❌ Erreur lors de l'installation de {package}:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print(f"❌ Timeout lors de l'installation de {package}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def main():
    """Installation principale"""
    print("=== Installation des dépendances ===")
    print()
    
    packages = ['django', 'reportlab', 'pillow']
    
    for package in packages:
        success = install_package(package)
        if not success:
            print(f"Échec de l'installation de {package}")
            return False
    
    print()
    print("🎉 Toutes les dépendances sont installées!")
    print()
    print("Étapes suivantes:")
    print("1. python manage.py makemigrations facturation")
    print("2. python manage.py migrate")
    print("3. python manage.py createsuperuser")
    print("4. python manage.py runserver")
    
    return True

if __name__ == '__main__':
    main()
