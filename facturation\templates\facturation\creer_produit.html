{% extends 'facturation/base.html' %}

{% block title %}{{ title }} - Facturation Pro{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'facturation:liste_produits' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Retour à la liste
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Informations du produit</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.nom.id_for_label }}" class="form-label">Nom du produit *</label>
                        {{ form.nom }}
                        {% if form.nom.errors %}
                            <div class="text-danger">{{ form.nom.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger">{{ form.description.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.prix_unitaire.id_for_label }}" class="form-label">Prix unitaire *</label>
                                <div class="input-group">
                                    {{ form.prix_unitaire }}
                                    <span class="input-group-text">€</span>
                                </div>
                                {% if form.prix_unitaire.errors %}
                                    <div class="text-danger">{{ form.prix_unitaire.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.unite.id_for_label }}" class="form-label">Unité</label>
                                {{ form.unite }}
                                {% if form.unite.errors %}
                                    <div class="text-danger">{{ form.unite.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.tva_taux.id_for_label }}" class="form-label">Taux de TVA</label>
                                <div class="input-group">
                                    {{ form.tva_taux }}
                                    <span class="input-group-text">%</span>
                                </div>
                                {% if form.tva_taux.errors %}
                                    <div class="text-danger">{{ form.tva_taux.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.actif }}
                            <label class="form-check-label" for="{{ form.actif.id_for_label }}">
                                Produit actif
                            </label>
                        </div>
                        {% if form.actif.errors %}
                            <div class="text-danger">{{ form.actif.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% url 'facturation:liste_produits' %}" class="btn btn-secondary me-2">
                            <i class="bi bi-x"></i> Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save"></i> Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Aide</h5>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-info-circle"></i> Les champs marqués d'un * sont obligatoires</li>
                        <li><i class="bi bi-info-circle"></i> Le prix sera utilisé par défaut dans les documents</li>
                        <li><i class="bi bi-info-circle"></i> Seuls les produits actifs apparaissent dans les formulaires</li>
                        <li><i class="bi bi-info-circle"></i> Le taux de TVA par défaut est de 20%</li>
                    </ul>
                </small>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Unités courantes</h5>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <ul class="list-unstyled">
                        <li>• unité</li>
                        <li>• heure</li>
                        <li>• jour</li>
                        <li>• kg</li>
                        <li>• mètre</li>
                        <li>• m²</li>
                        <li>• forfait</li>
                    </ul>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
