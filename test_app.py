#!/usr/bin/env python
"""
Script de test pour vérifier que l'application fonctionne
"""
import requests
import time
import subprocess
import sys
import os

def test_server():
    """Teste si le serveur Django répond"""
    try:
        response = requests.get('http://127.0.0.1:8000/', timeout=5)
        if response.status_code == 200:
            print("✅ Serveur Django accessible")
            return True
        else:
            print(f"❌ Serveur répond avec le code {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Impossible de contacter le serveur: {e}")
        return False

def test_admin():
    """Teste l'accès à l'interface d'administration"""
    try:
        response = requests.get('http://127.0.0.1:8000/admin/', timeout=5)
        if response.status_code == 200:
            print("✅ Interface d'administration accessible")
            return True
        else:
            print(f"❌ Admin répond avec le code {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Impossible d'accéder à l'admin: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("=== Test de l'application de facturation ===")
    print()
    
    print("Vérification des fichiers...")
    required_files = [
        'manage.py',
        'facturation/models.py',
        'facturation/views.py',
        'facturation/templates/facturation/base.html',
        'requirements.txt'
    ]
    
    all_files_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} manquant")
            all_files_exist = False
    
    if not all_files_exist:
        print("\n❌ Certains fichiers sont manquants")
        return False
    
    print("\n✅ Tous les fichiers requis sont présents")
    
    # Test de connectivité au serveur
    print("\nTest de connectivité au serveur...")
    if test_server():
        test_admin()
    else:
        print("💡 Assurez-vous que le serveur Django est démarré avec:")
        print("   python manage.py runserver")
    
    print("\n=== Fin des tests ===")
    return True

if __name__ == '__main__':
    try:
        # Installer requests si nécessaire
        import requests
    except ImportError:
        print("Installation de requests...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'requests'])
        import requests
    
    main()
