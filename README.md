# Système de Facturation Django

Une application web complète de gestion de facturation développée avec Django et ReportLab.

## Fonctionnalités

### 📋 Gestion des documents
- **Factures** : Création, modification et génération PDF
- **Devis** : Gestion complète des devis clients
- **Bons de livraison** : Suivi des livraisons
- **Numérotation automatique** des documents
- **Statuts** : Brouillon, Envoyé, Payé, Annulé

### 👥 Gestion des clients
- Informations complètes (particuliers et entreprises)
- Historique des documents par client
- Recherche et filtrage avancés

### 📦 Gestion des produits
- Catalogue de produits/services
- Prix unitaires et taux de TVA
- Unités de mesure personnalisables
- Statut actif/inactif

### 📄 Génération PDF
- **ReportLab** pour des PDF professionnels
- Templates personnalisables
- Logo et informations d'entreprise
- Calculs automatiques des totaux et TVA
- Archivage automatique des fichiers

### 🎨 Interface utilisateur
- **Bootstrap 5** pour un design moderne et responsive
- Dashboard avec statistiques
- Navigation intuitive
- Messages de confirmation et d'erreur

## Installation

### 1. Prérequis
- Python 3.8+
- pip

### 2. Installation des dépendances
```bash
pip install -r requirements.txt
```

### 3. Initialisation du projet
```bash
python init_project.py
```

Cette commande va :
- Créer les migrations Django
- Initialiser la base de données SQLite
- Créer un superutilisateur (admin/admin123)
- Ajouter des données d'exemple

### 4. Lancement du serveur
```bash
python manage.py runserver
```

## Utilisation

### Accès à l'application
- **Application principale** : http://127.0.0.1:8000/
- **Interface d'administration** : http://127.0.0.1:8000/admin/
- **Identifiants admin** : admin / admin123

### Workflow typique

1. **Configuration initiale**
   - Configurer les informations de votre entreprise dans l'admin
   - Ajouter vos clients
   - Créer votre catalogue de produits

2. **Création de documents**
   - Aller sur "Documents" > "Nouvelle facture/devis"
   - Sélectionner le client
   - Ajouter les lignes de produits
   - Le PDF est généré automatiquement

3. **Gestion des documents**
   - Visualiser tous les documents dans la liste
   - Modifier les documents existants
   - Télécharger les PDF
   - Suivre les statuts

## Structure du projet

```
projet_facturation/
├── facturation/                 # Application principale
│   ├── models.py               # Modèles de données
│   ├── views.py                # Vues Django
│   ├── forms.py                # Formulaires
│   ├── utils.py                # Génération PDF
│   ├── admin.py                # Interface admin
│   ├── urls.py                 # URLs de l'app
│   └── templates/              # Templates HTML
├── projet_facturation/         # Configuration Django
│   ├── settings.py             # Paramètres
│   └── urls.py                 # URLs principales
├── media/                      # Fichiers uploadés
│   └── pdfs/                   # PDFs générés
├── static/                     # Fichiers statiques
├── manage.py                   # Script Django
├── init_project.py             # Script d'initialisation
├── requirements.txt            # Dépendances
└── README.md                   # Documentation
```

## Modèles de données

### Client
- Informations personnelles et professionnelles
- Adresse complète
- Contacts (email, téléphone)

### Produit
- Nom et description
- Prix unitaire et unité
- Taux de TVA
- Statut actif/inactif

### Document
- Type (facture, devis, bon de livraison)
- Numéro automatique
- Client associé
- Dates (création, document, échéance)
- Statut et notes
- Totaux calculés automatiquement

### LigneDocument
- Produit et description
- Quantité et prix unitaire
- Taux de TVA
- Totaux calculés (HT, TVA, TTC)

### Entreprise
- Informations légales (SIRET, TVA)
- Coordonnées complètes
- Logo (optionnel)

## Personnalisation

### Modification des templates PDF
Éditez le fichier `facturation/utils.py` pour personnaliser :
- Mise en page des documents
- Couleurs et styles
- Informations affichées

### Ajout de nouveaux types de documents
1. Modifier les choix dans `models.py`
2. Ajouter les URLs correspondantes
3. Adapter les templates si nécessaire

### Personnalisation de l'interface
- Modifiez les templates dans `facturation/templates/`
- Ajoutez du CSS personnalisé
- Utilisez les classes Bootstrap pour le style

## Sécurité

⚠️ **Important pour la production** :
- Changez la `SECRET_KEY` dans `settings.py`
- Configurez `DEBUG = False`
- Utilisez une base de données robuste (PostgreSQL)
- Configurez HTTPS
- Sauvegardez régulièrement les données

## Support

Pour toute question ou problème :
1. Vérifiez les logs Django
2. Consultez la documentation Django
3. Vérifiez les permissions de fichiers pour les PDF

## Licence

Ce projet est fourni à des fins éducatives et peut être adapté selon vos besoins.
