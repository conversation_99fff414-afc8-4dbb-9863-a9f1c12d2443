{% extends 'facturation/base.html' %}

{% block title %}{{ title }} - Facturation Pro{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'facturation:liste_documents' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Retour à la liste
        </a>
    </div>
</div>

<form method="post" id="document-form">
    {% csrf_token %}
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informations du document</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.numero.id_for_label }}" class="form-label">Numéro</label>
                                {{ form.numero }}
                                {% if form.numero.errors %}
                                    <div class="text-danger">{{ form.numero.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.client.id_for_label }}" class="form-label">Client</label>
                                {{ form.client }}
                                {% if form.client.errors %}
                                    <div class="text-danger">{{ form.client.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.date_document.id_for_label }}" class="form-label">Date du document</label>
                                {{ form.date_document }}
                                {% if form.date_document.errors %}
                                    <div class="text-danger">{{ form.date_document.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.date_echeance.id_for_label }}" class="form-label">Date d'échéance</label>
                                {{ form.date_echeance }}
                                {% if form.date_echeance.errors %}
                                    <div class="text-danger">{{ form.date_echeance.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.statut.id_for_label }}" class="form-label">Statut</label>
                                {{ form.statut }}
                                {% if form.statut.errors %}
                                    <div class="text-danger">{{ form.statut.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger">{{ form.notes.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Lignes du document -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Lignes du document</h5>
                    <button type="button" class="btn btn-sm btn-success" id="add-line">
                        <i class="bi bi-plus"></i> Ajouter une ligne
                    </button>
                </div>
                <div class="card-body">
                    {{ formset.management_form }}
                    
                    <div id="formset-container">
                        {% for form in formset %}
                            <div class="ligne-form border p-3 mb-3" data-form-index="{{ forloop.counter0 }}">
                                {% if form.non_field_errors %}
                                    <div class="alert alert-danger">{{ form.non_field_errors }}</div>
                                {% endif %}
                                
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="{{ form.produit.id_for_label }}" class="form-label">Produit</label>
                                        {{ form.produit }}
                                        {% if form.produit.errors %}
                                            <div class="text-danger">{{ form.produit.errors }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-3">
                                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                                        {{ form.description }}
                                        {% if form.description.errors %}
                                            <div class="text-danger">{{ form.description.errors }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-2">
                                        <label for="{{ form.quantite.id_for_label }}" class="form-label">Quantité</label>
                                        {{ form.quantite }}
                                        {% if form.quantite.errors %}
                                            <div class="text-danger">{{ form.quantite.errors }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-2">
                                        <label for="{{ form.prix_unitaire.id_for_label }}" class="form-label">Prix unitaire</label>
                                        {{ form.prix_unitaire }}
                                        {% if form.prix_unitaire.errors %}
                                            <div class="text-danger">{{ form.prix_unitaire.errors }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-1">
                                        <label for="{{ form.tva_taux.id_for_label }}" class="form-label">TVA %</label>
                                        {{ form.tva_taux }}
                                        {% if form.tva_taux.errors %}
                                            <div class="text-danger">{{ form.tva_taux.errors }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-1 d-flex align-items-end">
                                        {% if form.DELETE %}
                                            {{ form.DELETE }}
                                            <label for="{{ form.DELETE.id_for_label }}" class="form-label">Supprimer</label>
                                        {% endif %}
                                        <button type="button" class="btn btn-sm btn-danger remove-line">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                {% for hidden in form.hidden_fields %}
                                    {{ hidden }}
                                {% endfor %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        <i class="bi bi-save"></i> Enregistrer
                    </button>
                    <a href="{% url 'facturation:liste_documents' %}" class="btn btn-secondary w-100">
                        <i class="bi bi-x"></i> Annuler
                    </a>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Aide</h5>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <ul class="list-unstyled">
                            <li><i class="bi bi-info-circle"></i> Sélectionnez un produit pour pré-remplir les champs</li>
                            <li><i class="bi bi-info-circle"></i> Les totaux seront calculés automatiquement</li>
                            <li><i class="bi bi-info-circle"></i> Un PDF sera généré après l'enregistrement</li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
// Gestion dynamique des lignes de formset
let formIndex = {{ formset.total_form_count }};

document.getElementById('add-line').addEventListener('click', function() {
    // Code pour ajouter une nouvelle ligne (simplifié)
    console.log('Ajouter une ligne');
});

document.addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-line') || e.target.closest('.remove-line')) {
        e.preventDefault();
        const ligne = e.target.closest('.ligne-form');
        const deleteCheckbox = ligne.querySelector('input[name$="-DELETE"]');
        if (deleteCheckbox) {
            deleteCheckbox.checked = true;
            ligne.style.display = 'none';
        }
    }
});

// Auto-remplissage des champs produit
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('produit-select')) {
        // Code pour auto-remplir les champs (à implémenter avec AJAX)
        console.log('Produit sélectionné:', e.target.value);
    }
});
</script>
{% endblock %}
