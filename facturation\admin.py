from django.contrib import admin
from .models import Client, Produit, Document, LigneDocument, Entreprise


class LigneDocumentInline(admin.TabularInline):
    model = LigneDocument
    extra = 1
    fields = ['produit', 'description', 'quantite', 'prix_unitaire', 'tva_taux']


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ['nom', 'prenom', 'entreprise', 'email', 'ville', 'date_creation']
    list_filter = ['ville', 'pays', 'date_creation']
    search_fields = ['nom', 'prenom', 'entreprise', 'email']
    ordering = ['nom', 'prenom']


@admin.register(Produit)
class ProduitAdmin(admin.ModelAdmin):
    list_display = ['nom', 'prix_unitaire', 'unite', 'tva_taux', 'actif', 'date_creation']
    list_filter = ['actif', 'tva_taux', 'date_creation']
    search_fields = ['nom', 'description']
    list_editable = ['prix_unitaire', 'actif']


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ['numero', 'type_document', 'client', 'date_document', 'statut', 'total_ttc']
    list_filter = ['type_document', 'statut', 'date_creation', 'date_document']
    search_fields = ['numero', 'client__nom', 'client__entreprise']
    inlines = [LigneDocumentInline]
    readonly_fields = ['sous_total', 'total_tva', 'total_ttc']
    
    fieldsets = (
        ('Informations générales', {
            'fields': ('numero', 'type_document', 'client', 'date_document', 'date_echeance', 'statut')
        }),
        ('Totaux', {
            'fields': ('sous_total', 'total_tva', 'total_ttc'),
            'classes': ('collapse',)
        }),
        ('Notes', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )


@admin.register(Entreprise)
class EntrepriseAdmin(admin.ModelAdmin):
    list_display = ['nom', 'ville', 'telephone', 'email']
    fieldsets = (
        ('Informations générales', {
            'fields': ('nom', 'logo')
        }),
        ('Adresse', {
            'fields': ('adresse', 'ville', 'code_postal', 'pays')
        }),
        ('Contact', {
            'fields': ('telephone', 'email')
        }),
        ('Informations légales', {
            'fields': ('siret', 'tva_numero')
        }),
    )
