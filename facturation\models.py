from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal


class Client(models.Model):
    nom = models.CharField(max_length=200)
    prenom = models.CharField(max_length=200, blank=True)
    entreprise = models.CharField(max_length=200, blank=True)
    email = models.EmailField()
    telephone = models.CharField(max_length=20, blank=True)
    adresse = models.TextField()
    ville = models.CharField(max_length=100)
    code_postal = models.CharField(max_length=10)
    pays = models.CharField(max_length=100, default='France')
    date_creation = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        if self.entreprise:
            return f"{self.entreprise} - {self.nom} {self.prenom}"
        return f"{self.nom} {self.prenom}"

    class Meta:
        ordering = ['nom', 'prenom']


class Produit(models.Model):
    nom = models.Char<PERSON><PERSON>(max_length=200)
    description = models.TextField(blank=True)
    prix_unitaire = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    unite = models.CharField(max_length=50, default='unité')
    tva_taux = models.DecimalField(max_digits=5, decimal_places=2, default=20.00)
    actif = models.BooleanField(default=True)
    date_creation = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.nom} - {self.prix_unitaire}€"

    class Meta:
        ordering = ['nom']


class Document(models.Model):
    TYPE_CHOICES = [
        ('facture', 'Facture'),
        ('devis', 'Devis'),
        ('bon_livraison', 'Bon de livraison'),
    ]
    
    STATUT_CHOICES = [
        ('brouillon', 'Brouillon'),
        ('envoye', 'Envoyé'),
        ('paye', 'Payé'),
        ('annule', 'Annulé'),
    ]

    numero = models.CharField(max_length=50, unique=True)
    type_document = models.CharField(max_length=20, choices=TYPE_CHOICES)
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    date_creation = models.DateTimeField(auto_now_add=True)
    date_document = models.DateField()
    date_echeance = models.DateField(null=True, blank=True)
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='brouillon')
    notes = models.TextField(blank=True)
    
    # Totaux calculés
    sous_total = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_tva = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_ttc = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Fichier PDF généré
    fichier_pdf = models.FileField(upload_to='pdfs/', blank=True, null=True)

    def __str__(self):
        return f"{self.get_type_document_display()} {self.numero} - {self.client}"

    def calculer_totaux(self):
        """Calcule les totaux du document"""
        lignes = self.lignes.all()
        self.sous_total = sum(ligne.total_ht for ligne in lignes)
        self.total_tva = sum(ligne.total_tva for ligne in lignes)
        self.total_ttc = self.sous_total + self.total_tva
        self.save()

    class Meta:
        ordering = ['-date_creation']


class LigneDocument(models.Model):
    document = models.ForeignKey(Document, related_name='lignes', on_delete=models.CASCADE)
    produit = models.ForeignKey(Produit, on_delete=models.CASCADE)
    description = models.CharField(max_length=500, blank=True)
    quantite = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    prix_unitaire = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    tva_taux = models.DecimalField(max_digits=5, decimal_places=2)
    
    # Totaux calculés
    total_ht = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_tva = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_ttc = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    def save(self, *args, **kwargs):
        # Calcul automatique des totaux
        self.total_ht = self.quantite * self.prix_unitaire
        self.total_tva = self.total_ht * (self.tva_taux / 100)
        self.total_ttc = self.total_ht + self.total_tva
        super().save(*args, **kwargs)
        
        # Recalculer les totaux du document parent
        self.document.calculer_totaux()

    def __str__(self):
        return f"{self.produit.nom} x {self.quantite}"

    class Meta:
        ordering = ['id']


class Entreprise(models.Model):
    """Informations de l'entreprise pour les documents"""
    nom = models.CharField(max_length=200)
    adresse = models.TextField()
    ville = models.CharField(max_length=100)
    code_postal = models.CharField(max_length=10)
    pays = models.CharField(max_length=100, default='France')
    telephone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    siret = models.CharField(max_length=20, blank=True)
    tva_numero = models.CharField(max_length=20, blank=True)
    logo = models.ImageField(upload_to='logos/', blank=True, null=True)

    def __str__(self):
        return self.nom

    class Meta:
        verbose_name = "Entreprise"
        verbose_name_plural = "Entreprises"
