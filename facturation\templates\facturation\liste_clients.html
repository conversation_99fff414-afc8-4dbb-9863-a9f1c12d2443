{% extends 'facturation/base.html' %}

{% block title %}Clients - Facturation Pro{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Clients</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'facturation:creer_client' %}" class="btn btn-primary">
            <i class="bi bi-plus"></i> Nouveau client
        </a>
    </div>
</div>

<!-- Recherche -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-8">
                <label for="search" class="form-label">Recherche</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ current_search }}" placeholder="Nom, prénom, entreprise, email...">
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="bi bi-search"></i> Rechercher
                </button>
                <a href="{% url 'facturation:liste_clients' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-x"></i> Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Liste des clients -->
<div class="card">
    <div class="card-body">
        {% if clients %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Entreprise</th>
                            <th>Email</th>
                            <th>Téléphone</th>
                            <th>Ville</th>
                            <th>Date création</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for client in clients %}
                        <tr>
                            <td>
                                <strong>{{ client.nom }} {{ client.prenom }}</strong>
                            </td>
                            <td>{{ client.entreprise|default:"-" }}</td>
                            <td>
                                {% if client.email %}
                                    <a href="mailto:{{ client.email }}">{{ client.email }}</a>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>{{ client.telephone|default:"-" }}</td>
                            <td>{{ client.ville }}</td>
                            <td>{{ client.date_creation|date:"d/m/Y" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="#" class="btn btn-outline-primary" title="Voir">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-outline-secondary" title="Modifier">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="{% url 'facturation:creer_facture' %}?client={{ client.pk }}" 
                                       class="btn btn-outline-success" title="Nouvelle facture">
                                        <i class="bi bi-receipt"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-people display-1 text-muted"></i>
                <h4 class="mt-3">Aucun client trouvé</h4>
                <p class="text-muted">Commencez par ajouter votre premier client.</p>
                <a href="{% url 'facturation:creer_client' %}" class="btn btn-primary">
                    <i class="bi bi-plus"></i> Créer un client
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
