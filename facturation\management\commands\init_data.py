from django.core.management.base import BaseCommand
from facturation.models import Entreprise, Client, Produit
from django.contrib.auth.models import User


class Command(BaseCommand):
    help = 'Initialise les données d\'exemple'

    def handle(self, *args, **options):
        self.stdout.write('Création des données d\'exemple...')
        
        # Créer l'entreprise
        if not Entreprise.objects.exists():
            entreprise = Entreprise.objects.create(
                nom="Mon Entreprise SARL",
                adresse="123 Rue de la République",
                ville="Paris",
                code_postal="75001",
                pays="France",
                telephone="01 23 45 67 89",
                email="<EMAIL>",
                siret="12345678901234",
                tva_numero="FR12345678901"
            )
            self.stdout.write(self.style.SUCCESS('Entreprise créée'))
        
        # Créer des clients d'exemple
        if not Client.objects.exists():
            clients = [
                {
                    'nom': '<PERSON><PERSON>',
                    'prenom': '<PERSON>',
                    'entreprise': '<PERSON>pont & Associés',
                    'email': '<EMAIL>',
                    'telephone': '01 23 45 67 89',
                    'adresse': '456 <PERSON> des Champs',
                    'ville': 'Lyon',
                    'code_postal': '69000'
                },
                {
                    'nom': 'Martin',
                    'prenom': 'Marie',
                    'email': '<EMAIL>',
                    'adresse': '789 Boulevard Saint-Germain',
                    'ville': 'Marseille',
                    'code_postal': '13000'
                }
            ]
            
            for client_data in clients:
                Client.objects.create(**client_data)
            self.stdout.write(self.style.SUCCESS(f'{len(clients)} clients créés'))
        
        # Créer des produits d'exemple
        if not Produit.objects.exists():
            produits = [
                {
                    'nom': 'Consultation',
                    'description': 'Consultation technique',
                    'prix_unitaire': 80.00,
                    'unite': 'heure',
                    'tva_taux': 20.00
                },
                {
                    'nom': 'Développement',
                    'description': 'Développement logiciel',
                    'prix_unitaire': 120.00,
                    'unite': 'jour',
                    'tva_taux': 20.00
                },
                {
                    'nom': 'Formation',
                    'description': 'Formation utilisateur',
                    'prix_unitaire': 500.00,
                    'unite': 'forfait',
                    'tva_taux': 20.00
                }
            ]
            
            for produit_data in produits:
                Produit.objects.create(**produit_data)
            self.stdout.write(self.style.SUCCESS(f'{len(produits)} produits créés'))
        
        # Créer un superutilisateur
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
            self.stdout.write(self.style.SUCCESS('Superutilisateur créé: admin / admin123'))
        
        self.stdout.write(self.style.SUCCESS('Initialisation terminée!'))
