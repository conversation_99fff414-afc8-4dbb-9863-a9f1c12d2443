#!/usr/bin/env python
"""
Script d'initialisation du projet de facturation
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    """Configure Django"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'projet_facturation.settings')
    django.setup()

def create_migrations():
    """Créer les migrations"""
    print("Création des migrations...")
    execute_from_command_line(['manage.py', 'makemigrations'])

def migrate():
    """Appliquer les migrations"""
    print("Application des migrations...")
    execute_from_command_line(['manage.py', 'migrate'])

def create_superuser():
    """Créer un superutilisateur"""
    print("Création d'un superutilisateur...")
    from django.contrib.auth.models import User
    
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
        print("Superutilisateur créé: admin / admin123")
    else:
        print("Superutilisateur déjà existant")

def create_sample_data():
    """Créer des données d'exemple"""
    print("Création des données d'exemple...")
    
    from facturation.models import Entreprise, Client, Produit, Document, LigneDocument
    from datetime import date, timedelta
    
    # Créer l'entreprise
    if not Entreprise.objects.exists():
        entreprise = Entreprise.objects.create(
            nom="Mon Entreprise SARL",
            adresse="123 Rue de la République",
            ville="Paris",
            code_postal="75001",
            pays="France",
            telephone="01 23 45 67 89",
            email="<EMAIL>",
            siret="12345678901234",
            tva_numero="FR12345678901"
        )
        print("Entreprise créée")
    
    # Créer des clients d'exemple
    if not Client.objects.exists():
        clients = [
            {
                'nom': 'Dupont',
                'prenom': 'Jean',
                'entreprise': 'Dupont & Associés',
                'email': '<EMAIL>',
                'telephone': '01 23 45 67 89',
                'adresse': '456 Avenue des Champs',
                'ville': 'Lyon',
                'code_postal': '69000'
            },
            {
                'nom': 'Martin',
                'prenom': 'Marie',
                'email': '<EMAIL>',
                'adresse': '789 Boulevard Saint-Germain',
                'ville': 'Marseille',
                'code_postal': '13000'
            }
        ]
        
        for client_data in clients:
            Client.objects.create(**client_data)
        print(f"{len(clients)} clients créés")
    
    # Créer des produits d'exemple
    if not Produit.objects.exists():
        produits = [
            {
                'nom': 'Consultation',
                'description': 'Consultation technique',
                'prix_unitaire': 80.00,
                'unite': 'heure',
                'tva_taux': 20.00
            },
            {
                'nom': 'Développement',
                'description': 'Développement logiciel',
                'prix_unitaire': 120.00,
                'unite': 'jour',
                'tva_taux': 20.00
            },
            {
                'nom': 'Formation',
                'description': 'Formation utilisateur',
                'prix_unitaire': 500.00,
                'unite': 'forfait',
                'tva_taux': 20.00
            }
        ]
        
        for produit_data in produits:
            Produit.objects.create(**produit_data)
        print(f"{len(produits)} produits créés")

def main():
    """Fonction principale"""
    print("=== Initialisation du projet de facturation ===")
    
    try:
        setup_django()
        create_migrations()
        migrate()
        create_superuser()
        create_sample_data()
        
        print("\n=== Initialisation terminée avec succès! ===")
        print("Vous pouvez maintenant:")
        print("1. Lancer le serveur: python manage.py runserver")
        print("2. Accéder à l'admin: http://127.0.0.1:8000/admin/")
        print("3. Accéder à l'application: http://127.0.0.1:8000/")
        print("4. Identifiants admin: admin / admin123")
        
    except Exception as e:
        print(f"Erreur lors de l'initialisation: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
