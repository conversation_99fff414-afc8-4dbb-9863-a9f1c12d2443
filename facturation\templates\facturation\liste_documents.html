{% extends 'facturation/base.html' %}

{% block title %}Documents - Facturation Pro{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Documents</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'facturation:creer_facture' %}" class="btn btn-primary">
                <i class="bi bi-plus"></i> Nouvelle facture
            </a>
            <a href="{% url 'facturation:creer_devis' %}" class="btn btn-info">
                <i class="bi bi-plus"></i> Nouveau devis
            </a>
            <a href="{% url 'facturation:creer_bon_livraison' %}" class="btn btn-success">
                <i class="bi bi-plus"></i> <PERSON> livraison
            </a>
        </div>
    </div>
</div>

<!-- Filtres -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Recherche</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ current_search }}" placeholder="Numéro, client...">
            </div>
            <div class="col-md-3">
                <label for="type" class="form-label">Type de document</label>
                <select class="form-control" id="type" name="type">
                    <option value="">Tous les types</option>
                    {% for value, label in type_choices %}
                        <option value="{{ value }}" {% if current_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="statut" class="form-label">Statut</label>
                <select class="form-control" id="statut" name="statut">
                    <option value="">Tous les statuts</option>
                    {% for value, label in statut_choices %}
                        <option value="{{ value }}" {% if current_statut == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="bi bi-search"></i> Filtrer
                </button>
                <a href="{% url 'facturation:liste_documents' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-x"></i> Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Liste des documents -->
<div class="card">
    <div class="card-body">
        {% if documents %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Numéro</th>
                            <th>Type</th>
                            <th>Client</th>
                            <th>Date</th>
                            <th>Échéance</th>
                            <th>Statut</th>
                            <th>Montant TTC</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in documents %}
                        <tr>
                            <td>
                                <a href="{% url 'facturation:detail_document' document.pk %}" class="text-decoration-none">
                                    <strong>{{ document.numero }}</strong>
                                </a>
                            </td>
                            <td>
                                {% if document.type_document == 'facture' %}
                                    <span class="badge bg-primary">{{ document.get_type_document_display }}</span>
                                {% elif document.type_document == 'devis' %}
                                    <span class="badge bg-info">{{ document.get_type_document_display }}</span>
                                {% else %}
                                    <span class="badge bg-success">{{ document.get_type_document_display }}</span>
                                {% endif %}
                            </td>
                            <td>{{ document.client }}</td>
                            <td>{{ document.date_document|date:"d/m/Y" }}</td>
                            <td>
                                {% if document.date_echeance %}
                                    {{ document.date_echeance|date:"d/m/Y" }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if document.statut == 'brouillon' %}
                                    <span class="badge bg-secondary">{{ document.get_statut_display }}</span>
                                {% elif document.statut == 'envoye' %}
                                    <span class="badge bg-warning">{{ document.get_statut_display }}</span>
                                {% elif document.statut == 'paye' %}
                                    <span class="badge bg-success">{{ document.get_statut_display }}</span>
                                {% else %}
                                    <span class="badge bg-danger">{{ document.get_statut_display }}</span>
                                {% endif %}
                            </td>
                            <td><strong>{{ document.total_ttc|floatformat:2 }}€</strong></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'facturation:detail_document' document.pk %}" 
                                       class="btn btn-outline-primary" title="Voir">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{% url 'facturation:modifier_document' document.pk %}" 
                                       class="btn btn-outline-secondary" title="Modifier">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="{% url 'facturation:telecharger_pdf' document.pk %}" 
                                       class="btn btn-outline-success" title="Télécharger PDF">
                                        <i class="bi bi-download"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-file-text display-1 text-muted"></i>
                <h4 class="mt-3">Aucun document trouvé</h4>
                <p class="text-muted">Commencez par créer votre premier document.</p>
                <div class="mt-3">
                    <a href="{% url 'facturation:creer_facture' %}" class="btn btn-primary me-2">
                        <i class="bi bi-plus"></i> Créer une facture
                    </a>
                    <a href="{% url 'facturation:creer_devis' %}" class="btn btn-info">
                        <i class="bi bi-plus"></i> Créer un devis
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
