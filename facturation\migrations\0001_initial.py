# Generated by Django 5.2.1 on 2025-05-29 10:09

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Client',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.Char<PERSON>ield(max_length=200)),
                ('prenom', models.Char<PERSON>ield(blank=True, max_length=200)),
                ('entreprise', models.Char<PERSON>ield(blank=True, max_length=200)),
                ('email', models.EmailField(max_length=254)),
                ('telephone', models.CharField(blank=True, max_length=20)),
                ('adresse', models.TextField()),
                ('ville', models.CharField(max_length=100)),
                ('code_postal', models.Cha<PERSON><PERSON><PERSON>(max_length=10)),
                ('pays', models.Char<PERSON><PERSON>(default='France', max_length=100)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['nom', 'prenom'],
            },
        ),
        migrations.CreateModel(
            name='Entreprise',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=200)),
                ('adresse', models.TextField()),
                ('ville', models.CharField(max_length=100)),
                ('code_postal', models.CharField(max_length=10)),
                ('pays', models.CharField(default='France', max_length=100)),
                ('telephone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('siret', models.CharField(blank=True, max_length=20)),
                ('tva_numero', models.CharField(blank=True, max_length=20)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='logos/')),
            ],
            options={
                'verbose_name': 'Entreprise',
                'verbose_name_plural': 'Entreprises',
            },
        ),
        migrations.CreateModel(
            name='Produit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('prix_unitaire', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('unite', models.CharField(default='unité', max_length=50)),
                ('tva_taux', models.DecimalField(decimal_places=2, default=20.0, max_digits=5)),
                ('actif', models.BooleanField(default=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['nom'],
            },
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('numero', models.CharField(max_length=50, unique=True)),
                ('type_document', models.CharField(choices=[('facture', 'Facture'), ('devis', 'Devis'), ('bon_livraison', 'Bon de livraison')], max_length=20)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_document', models.DateField()),
                ('date_echeance', models.DateField(blank=True, null=True)),
                ('statut', models.CharField(choices=[('brouillon', 'Brouillon'), ('envoye', 'Envoyé'), ('paye', 'Payé'), ('annule', 'Annulé')], default='brouillon', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('sous_total', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_tva', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_ttc', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('fichier_pdf', models.FileField(blank=True, null=True, upload_to='pdfs/')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='facturation.client')),
            ],
            options={
                'ordering': ['-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='LigneDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(blank=True, max_length=500)),
                ('quantite', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('prix_unitaire', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('tva_taux', models.DecimalField(decimal_places=2, max_digits=5)),
                ('total_ht', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_tva', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_ttc', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lignes', to='facturation.document')),
                ('produit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='facturation.produit')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
