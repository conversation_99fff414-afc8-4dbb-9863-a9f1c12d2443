from django.core.management.base import BaseCommand
from facturation.models import Entreprise
from django.core.files.base import ContentFile
import os


class Command(BaseCommand):
    help = 'إعداد معلومات الشركة مع لوجو تجريبي'

    def handle(self, *args, **options):
        self.stdout.write('إعداد معلومات الشركة...')
        
        # إنشاء أو تحديث معلومات الشركة
        entreprise, created = Entreprise.objects.get_or_create(
            defaults={
                'nom': 'شركة الفوترة المحترفة',
                'adresse': '123 شارع التجارة، الحي التجاري',
                'ville': 'الرياض',
                'code_postal': '12345',
                'pays': 'المملكة العربية السعودية',
                'telephone': '+966 11 123 4567',
                'email': '<EMAIL>',
                'siret': '1234567890123',
                'tva_numero': 'SA123456789012345',
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS('تم إنشاء معلومات الشركة بنجاح!')
            )
        else:
            self.stdout.write(
                self.style.WARNING('معلومات الشركة موجودة مسبقاً')
            )
        
        # إضافة لوجو تجريبي إذا لم يكن موجود
        if not entreprise.logo:
            logo_path = 'static/images/logo-exemple.svg'
            if os.path.exists(logo_path):
                with open(logo_path, 'rb') as f:
                    entreprise.logo.save(
                        'logo-company.svg',
                        ContentFile(f.read()),
                        save=True
                    )
                self.stdout.write(
                    self.style.SUCCESS('تم إضافة اللوجو التجريبي!')
                )
            else:
                self.stdout.write(
                    self.style.WARNING('ملف اللوجو التجريبي غير موجود')
                )
        
        self.stdout.write(
            self.style.SUCCESS('تم إعداد الشركة بنجاح! يمكنك الآن إنشاء فواتير جميلة.')
        )
