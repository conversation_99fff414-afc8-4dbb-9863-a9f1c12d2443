/* ===== THÈME CORPORATE/PROFESSIONNEL ===== */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #95a5a6;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    
    --corporate-blue: #34495e;
    --corporate-gray: #7f8c8d;
    --corporate-light: #bdc3c7;
}

/* Navigation corporate */
.navbar-dark {
    background-color: var(--primary-color) !important;
    border-bottom: 3px solid var(--warning-color);
}

.navbar-brand {
    font-family: 'Georgia', serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: white !important;
}

.nav-link {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

/* Cartes corporate */
.card {
    border: 1px solid #bdc3c7;
    border-radius: 0.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: white;
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--primary-color);
}

/* Cartes de statistiques corporate */
.card-stats {
    border-top: 4px solid var(--primary-color);
    border-left: none;
}

.card-stats.border-left-primary {
    border-top-color: var(--primary-color);
}

.card-stats.border-left-success {
    border-top-color: var(--success-color);
}

.card-stats.border-left-info {
    border-top-color: var(--info-color);
}

.card-stats.border-left-warning {
    border-top-color: var(--warning-color);
}

/* Boutons corporate */
.btn {
    border-radius: 0.25rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid transparent;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--corporate-blue);
    border-color: var(--corporate-blue);
    transform: none;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Tableaux corporate */
.table {
    border: 1px solid #bdc3c7;
    border-radius: 0.25rem;
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.85rem;
}

.table tbody tr {
    border-bottom: 1px solid #ecf0f1;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: none;
}

.table tbody tr:nth-child(even) {
    background-color: #fafafa;
}

/* Formulaires corporate */
.form-control, .form-select {
    border-radius: 0.25rem;
    border: 2px solid #bdc3c7;
    font-weight: 500;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
    transform: none;
}

.form-label {
    font-weight: 700;
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

/* Badges corporate */
.badge {
    border-radius: 0.25rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Contenu principal */
.main-content {
    background-color: #f8f9fa;
    background-image: 
        linear-gradient(45deg, #f8f9fa 25%, transparent 25%), 
        linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), 
        linear-gradient(45deg, transparent 75%, #f8f9fa 75%), 
        linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* Titres corporate */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Georgia', serif;
    color: var(--primary-color);
    font-weight: 700;
}

.h2 {
    border-bottom: 3px solid var(--warning-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

/* Alertes corporate */
.alert {
    border-radius: 0.25rem;
    border: 2px solid;
    font-weight: 500;
}

.alert-primary {
    border-color: var(--primary-color);
    background-color: rgba(44, 62, 80, 0.1);
}

.alert-success {
    border-color: var(--success-color);
    background-color: rgba(39, 174, 96, 0.1);
}

/* Désactiver les animations pour un look plus professionnel */
.card:hover {
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: none;
}

/* Sidebar corporate si utilisée */
.sidebar {
    background-color: var(--light-color);
    border-right: 3px solid var(--primary-color);
}

/* Dropdown corporate */
.dropdown-menu {
    border: 2px solid var(--primary-color);
    border-radius: 0.25rem;
}

.dropdown-item {
    font-weight: 500;
    color: var(--primary-color);
}

.dropdown-item:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}
