/* ===== THÈME MODERNE ===== */

:root {
    --primary-color: #6366f1;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Navigation avec gradient */
.navbar-dark {
    background: var(--gradient-primary) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-size: 1.75rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Cartes modernes */
.card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 1rem 1rem 0 0 !important;
    font-weight: 700;
    font-size: 1.1rem;
}

/* Cartes de statistiques avec gradients */
.card-stats.border-left-primary {
    background: linear-gradient(135deg, rgba(102, 102, 241, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
    border-left: 4px solid var(--primary-color);
}

.card-stats.border-left-success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
    border-left: 4px solid var(--success-color);
}

.card-stats.border-left-info {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
    border-left: 4px solid var(--info-color);
}

.card-stats.border-left-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
    border-left: 4px solid var(--warning-color);
}

/* Boutons modernes */
.btn {
    border-radius: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    box-shadow: 0 4px 6px -1px rgba(102, 102, 241, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(102, 102, 241, 0.4);
}

.btn-success {
    background: var(--gradient-success);
    border: none;
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
}

.btn-info {
    background: var(--gradient-info);
    border: none;
    box-shadow: 0 4px 6px -1px rgba(6, 182, 212, 0.3);
}

.btn-warning {
    background: var(--gradient-warning);
    border: none;
    box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.3);
}

/* Tableaux modernes */
.table {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: var(--gradient-primary);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.table tbody tr {
    border: none;
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: linear-gradient(90deg, rgba(102, 102, 241, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
    transform: scale(1.01);
}

/* Formulaires modernes */
.form-control, .form-select {
    border-radius: 0.75rem;
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 102, 241, 0.1);
    transform: scale(1.02);
}

/* Badges modernes */
.badge {
    border-radius: 0.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Contenu principal */
.main-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    position: relative;
}

.main-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.main-content > * {
    position: relative;
    z-index: 1;
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: slideInUp 0.6s ease-out;
}

/* Effets de survol */
.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    transform: translateY(-1px);
}
