# 🚀 Démarrage Rapide - Application de Facturation

## ⚡ Instructions Simples

### 1. Ouvrir un terminal
- **Windows :** A<PERSON><PERSON><PERSON> sur `Win + R`, tapez `cmd`, Entrée
- **Mac/Linux :** Ouvrez le Terminal

### 2. Aller dans le dossier du projet
```bash
cd "C:\Users\<USER>\Documents\augment-projects\FCBL"
```

### 3. Installer les dépendances (une par une)
```bash
pip install django
pip install reportlab  
pip install pillow
```

### 4. <PERSON><PERSON>er la base de données
```bash
python manage.py makemigrations facturation
python manage.py migrate
```

### 5. C<PERSON>er un administrateur
```bash
python manage.py createsuperuser
```
- Username: `admin`
- Email: `<EMAIL>`  
- Password: `admin123`

### 6. Démarrer l'application
```bash
python manage.py runserver
```

### 7. Ouv<PERSON><PERSON> dans le navigateur
- **Application :** http://127.0.0.1:8000/
- **Admin :** http://127.0.0.1:8000/admin/

## 🎯 Ce que vous pouvez faire

### Dans l'application (http://127.0.0.1:8000/)
- ✅ Voir le dashboard
- ✅ Créer des clients
- ✅ Créer des produits
- ✅ Générer des factures
- ✅ Télécharger des PDF

### Dans l'admin (http://127.0.0.1:8000/admin/)
- ✅ Gérer tous les données
- ✅ Configurer l'entreprise
- ✅ Voir les statistiques

## 🔧 Si ça ne marche pas

### Problème 1: "pip n'est pas reconnu"
**Solution :** Réinstallez Python en cochant "Add to PATH"

### Problème 2: "python n'est pas reconnu"  
**Solution :** Utilisez `py` au lieu de `python`

### Problème 3: Erreur d'installation
**Solution :** Exécutez en tant qu'administrateur

### Problème 4: Erreur Django
**Solution :** 
```bash
pip uninstall django
pip install django
```

## 📋 Checklist Rapide

- [ ] Terminal ouvert
- [ ] Dans le bon dossier
- [ ] Django installé (`pip install django`)
- [ ] ReportLab installé (`pip install reportlab`)
- [ ] Pillow installé (`pip install pillow`)
- [ ] Migrations créées (`python manage.py makemigrations facturation`)
- [ ] Base migrée (`python manage.py migrate`)
- [ ] Admin créé (`python manage.py createsuperuser`)
- [ ] Serveur démarré (`python manage.py runserver`)
- [ ] Application ouverte dans le navigateur

## 🎉 Test Final

1. Allez sur http://127.0.0.1:8000/
2. Cliquez sur "Nouveau client"
3. Ajoutez un client
4. Cliquez sur "Nouveau produit"  
5. Ajoutez un produit
6. Cliquez sur "Nouvelle facture"
7. Créez une facture
8. Téléchargez le PDF

Si tout fonctionne, félicitations ! 🎊

## 📞 Aide

Si vous êtes bloqué :
1. Lisez `INSTALLATION_MANUELLE.md`
2. Consultez `GUIDE_DEPANNAGE.md`
3. Exécutez `python test_python.py` pour diagnostiquer

## 🔄 Redémarrer l'application

Pour redémarrer plus tard :
1. Ouvrez un terminal
2. Allez dans le dossier : `cd "C:\Users\<USER>\Documents\augment-projects\FCBL"`
3. Démarrez : `python manage.py runserver`
4. Ouvrez : http://127.0.0.1:8000/
