from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.http import HttpResponse, FileResponse
from django.urls import reverse
from django.db.models import Q
from .models import Document, Client, Produit, Entreprise
from .forms import DocumentForm, ClientForm, ProduitForm, LigneDocumentFormSet
from .utils import generer_pdf
import os


def dashboard(request):
    """Page d'accueil avec statistiques"""
    context = {
        'total_clients': Client.objects.count(),
        'total_produits': Produit.objects.filter(actif=True).count(),
        'factures_recentes': Document.objects.filter(type_document='facture').order_by('-date_creation')[:5],
        'devis_recents': Document.objects.filter(type_document='devis').order_by('-date_creation')[:5],
    }
    return render(request, 'facturation/dashboard.html', context)


def liste_documents(request):
    """Liste tous les documents avec filtres"""
    documents = Document.objects.all()
    
    # Filtres
    type_doc = request.GET.get('type')
    statut = request.GET.get('statut')
    search = request.GET.get('search')
    
    if type_doc:
        documents = documents.filter(type_document=type_doc)
    if statut:
        documents = documents.filter(statut=statut)
    if search:
        documents = documents.filter(
            Q(numero__icontains=search) |
            Q(client__nom__icontains=search) |
            Q(client__entreprise__icontains=search)
        )
    
    context = {
        'documents': documents,
        'type_choices': Document.TYPE_CHOICES,
        'statut_choices': Document.STATUT_CHOICES,
        'current_type': type_doc,
        'current_statut': statut,
        'current_search': search,
    }
    return render(request, 'facturation/liste_documents.html', context)


def creer_document(request, type_doc):
    """Créer un nouveau document"""
    if request.method == 'POST':
        form = DocumentForm(request.POST)
        formset = LigneDocumentFormSet(request.POST)
        
        if form.is_valid() and formset.is_valid():
            document = form.save(commit=False)
            document.type_document = type_doc
            document.save()
            
            lignes = formset.save(commit=False)
            for ligne in lignes:
                ligne.document = document
                ligne.save()
            
            # Générer le PDF
            try:
                generer_pdf(document)
                messages.success(request, f'{document.get_type_document_display()} créé(e) avec succès!')
            except Exception as e:
                messages.warning(request, f'Document créé mais erreur lors de la génération du PDF: {str(e)}')
            
            return redirect('facturation:detail_document', pk=document.pk)
    else:
        form = DocumentForm()
        formset = LigneDocumentFormSet()
    
    context = {
        'form': form,
        'formset': formset,
        'type_doc': type_doc,
        'title': f'Créer un(e) {dict(Document.TYPE_CHOICES)[type_doc]}',
    }
    return render(request, 'facturation/creer_document.html', context)


def detail_document(request, pk):
    """Détail d'un document"""
    document = get_object_or_404(Document, pk=pk)
    context = {
        'document': document,
        'lignes': document.lignes.all(),
    }
    return render(request, 'facturation/detail_document.html', context)


def modifier_document(request, pk):
    """Modifier un document existant"""
    document = get_object_or_404(Document, pk=pk)
    
    if request.method == 'POST':
        form = DocumentForm(request.POST, instance=document)
        formset = LigneDocumentFormSet(request.POST, instance=document)
        
        if form.is_valid() and formset.is_valid():
            form.save()
            formset.save()
            
            # Régénérer le PDF
            try:
                generer_pdf(document)
                messages.success(request, 'Document modifié avec succès!')
            except Exception as e:
                messages.warning(request, f'Document modifié mais erreur lors de la génération du PDF: {str(e)}')
            
            return redirect('facturation:detail_document', pk=document.pk)
    else:
        form = DocumentForm(instance=document)
        formset = LigneDocumentFormSet(instance=document)
    
    context = {
        'form': form,
        'formset': formset,
        'document': document,
        'title': f'Modifier {document.get_type_document_display()} {document.numero}',
    }
    return render(request, 'facturation/modifier_document.html', context)


def telecharger_pdf(request, pk):
    """Télécharger le PDF d'un document"""
    document = get_object_or_404(Document, pk=pk)
    
    if not document.fichier_pdf or not os.path.exists(document.fichier_pdf.path):
        try:
            generer_pdf(document)
        except Exception as e:
            messages.error(request, f'Erreur lors de la génération du PDF: {str(e)}')
            return redirect('facturation:detail_document', pk=document.pk)
    
    if document.fichier_pdf and os.path.exists(document.fichier_pdf.path):
        return FileResponse(
            open(document.fichier_pdf.path, 'rb'),
            as_attachment=True,
            filename=f'{document.get_type_document_display()}_{document.numero}.pdf'
        )
    else:
        messages.error(request, 'Fichier PDF non trouvé.')
        return redirect('facturation:detail_document', pk=document.pk)


def liste_clients(request):
    """Liste des clients"""
    clients = Client.objects.all()
    search = request.GET.get('search')
    
    if search:
        clients = clients.filter(
            Q(nom__icontains=search) |
            Q(prenom__icontains=search) |
            Q(entreprise__icontains=search) |
            Q(email__icontains=search)
        )
    
    context = {
        'clients': clients,
        'current_search': search,
    }
    return render(request, 'facturation/liste_clients.html', context)


def creer_client(request):
    """Créer un nouveau client"""
    if request.method == 'POST':
        form = ClientForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Client créé avec succès!')
            return redirect('facturation:liste_clients')
    else:
        form = ClientForm()
    
    context = {
        'form': form,
        'title': 'Créer un client',
    }
    return render(request, 'facturation/creer_client.html', context)


def liste_produits(request):
    """Liste des produits"""
    produits = Produit.objects.filter(actif=True)
    search = request.GET.get('search')
    
    if search:
        produits = produits.filter(
            Q(nom__icontains=search) |
            Q(description__icontains=search)
        )
    
    context = {
        'produits': produits,
        'current_search': search,
    }
    return render(request, 'facturation/liste_produits.html', context)


def creer_produit(request):
    """Créer un nouveau produit"""
    if request.method == 'POST':
        form = ProduitForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Produit créé avec succès!')
            return redirect('facturation:liste_produits')
    else:
        form = ProduitForm()
    
    context = {
        'form': form,
        'title': 'Créer un produit',
    }
    return render(request, 'facturation/creer_produit.html', context)
