{% extends 'facturation/base.html' %}

{% block title %}Dashboard - Facturation Pro{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard</h1>
</div>

<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Clients
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_clients }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Produits actifs
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_produits }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Factures récentes
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ factures_recentes|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-receipt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Devis récents
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ devis_recents|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-file-text fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Actions rapides</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'facturation:creer_facture' %}" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-receipt"></i><br>
                            Nouvelle Facture
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'facturation:creer_devis' %}" class="btn btn-info btn-lg w-100">
                            <i class="bi bi-file-text"></i><br>
                            Nouveau Devis
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'facturation:creer_bon_livraison' %}" class="btn btn-success btn-lg w-100">
                            <i class="bi bi-truck"></i><br>
                            Bon de Livraison
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'facturation:creer_client' %}" class="btn btn-secondary btn-lg w-100">
                            <i class="bi bi-person-plus"></i><br>
                            Nouveau Client
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Documents récents -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Factures récentes</h5>
                <a href="{% url 'facturation:liste_documents' %}?type=facture" class="btn btn-sm btn-outline-primary">Voir tout</a>
            </div>
            <div class="card-body">
                {% if factures_recentes %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Numéro</th>
                                    <th>Client</th>
                                    <th>Date</th>
                                    <th>Montant</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for facture in factures_recentes %}
                                <tr>
                                    <td>
                                        <a href="{% url 'facturation:detail_document' facture.pk %}">{{ facture.numero }}</a>
                                    </td>
                                    <td>{{ facture.client }}</td>
                                    <td>{{ facture.date_document|date:"d/m/Y" }}</td>
                                    <td>{{ facture.total_ttc }}€</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">Aucune facture récente</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Devis récents</h5>
                <a href="{% url 'facturation:liste_documents' %}?type=devis" class="btn btn-sm btn-outline-info">Voir tout</a>
            </div>
            <div class="card-body">
                {% if devis_recents %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Numéro</th>
                                    <th>Client</th>
                                    <th>Date</th>
                                    <th>Montant</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for devis in devis_recents %}
                                <tr>
                                    <td>
                                        <a href="{% url 'facturation:detail_document' devis.pk %}">{{ devis.numero }}</a>
                                    </td>
                                    <td>{{ devis.client }}</td>
                                    <td>{{ devis.date_document|date:"d/m/Y" }}</td>
                                    <td>{{ devis.total_ttc }}€</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">Aucun devis récent</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
